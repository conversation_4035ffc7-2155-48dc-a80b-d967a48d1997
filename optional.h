#pragma once
#ifndef OPTIONAL_H
#define OPTIONAL_H

#include <utility>
#include <stdexcept>

/**
 * @brief 简单的Optional类，用于替代C++17的std::optional
 * @tparam T 包含的值类型
 */
template<typename T>
class Optional {
public:
    /**
     * @brief 默认构造函数，创建一个空的Optional
     */
    Optional() : m_hasValue(false), m_value() {}

    /**
     * @brief 从值构造
     * @param value 要存储的值
     */
    Optional(const T& value) : m_hasValue(true), m_value(value) {}

    /**
     * @brief 从值构造（移动语义）
     * @param value 要存储的值
     */
    Optional(T&& value) : m_hasValue(true), m_value(std::move(value)) {}

    /**
     * @brief 复制构造函数
     * @param other 要复制的Optional
     */
    Optional(const Optional& other) : m_hasValue(other.m_hasValue) {
        if (m_hasValue) {
            m_value = other.m_value;
        }
    }

    /**
     * @brief 移动构造函数
     * @param other 要移动的Optional
     */
    Optional(Optional&& other) noexcept : m_hasValue(other.m_hasValue) {
        if (m_hasValue) {
            m_value = std::move(other.m_value);
        }
        other.m_hasValue = false;
    }

    /**
     * @brief 复制赋值运算符
     * @param other 要复制的Optional
     * @return 自身引用
     */
    Optional& operator=(const Optional& other) {
        if (this != &other) {
            m_hasValue = other.m_hasValue;
            if (m_hasValue) {
                m_value = other.m_value;
            }
        }
        return *this;
    }

    /**
     * @brief 移动赋值运算符
     * @param other 要移动的Optional
     * @return 自身引用
     */
    Optional& operator=(Optional&& other) noexcept {
        if (this != &other) {
            m_hasValue = other.m_hasValue;
            if (m_hasValue) {
                m_value = std::move(other.m_value);
            }
            other.m_hasValue = false;
        }
        return *this;
    }

    /**
     * @brief 从值赋值
     * @param value 要存储的值
     * @return 自身引用
     */
    Optional& operator=(const T& value) {
        m_hasValue = true;
        m_value = value;
        return *this;
    }

    /**
     * @brief 从值赋值（移动语义）
     * @param value 要存储的值
     * @return 自身引用
     */
    Optional& operator=(T&& value) {
        m_hasValue = true;
        m_value = std::move(value);
        return *this;
    }

    /**
     * @brief 析构函数
     */
    ~Optional() = default;

    /**
     * @brief 检查是否有值
     * @return 是否有值
     */
    bool hasValue() const {
        return m_hasValue;
    }

    /**
     * @brief 转换为bool，检查是否有值
     * @return 是否有值
     */
    explicit operator bool() const {
        return m_hasValue;
    }

    /**
     * @brief 获取值
     * @return 值的引用
     * @throw std::runtime_error 如果没有值
     */
    T& value() {
        if (!m_hasValue) {
            throw std::runtime_error("Optional has no value");
        }
        return m_value;
    }

    /**
     * @brief 获取值（常量版本）
     * @return 值的常量引用
     * @throw std::runtime_error 如果没有值
     */
    const T& value() const {
        if (!m_hasValue) {
            throw std::runtime_error("Optional has no value");
        }
        return m_value;
    }

    /**
     * @brief 获取值，如果没有值则返回默认值
     * @param defaultValue 默认值
     * @return 值或默认值
     */
    T valueOr(const T& defaultValue) const {
        return m_hasValue ? m_value : defaultValue;
    }

    /**
     * @brief 解引用操作符
     * @return 值的引用
     * @throw std::runtime_error 如果没有值
     */
    T& operator*() {
        return value();
    }

    /**
     * @brief 解引用操作符（常量版本）
     * @return 值的常量引用
     * @throw std::runtime_error 如果没有值
     */
    const T& operator*() const {
        return value();
    }

    /**
     * @brief 箭头操作符
     * @return 值的指针
     * @throw std::runtime_error 如果没有值
     */
    T* operator->() {
        return &value();
    }

    /**
     * @brief 箭头操作符（常量版本）
     * @return 值的常量指针
     * @throw std::runtime_error 如果没有值
     */
    const T* operator->() const {
        return &value();
    }

    /**
     * @brief 重置为空
     */
    void reset() {
        m_hasValue = false;
    }

    /**
     * @brief 创建一个空的Optional
     * @return 空的Optional
     */
    static Optional<T> nullopt() {
        return Optional<T>();
    }

private:
    bool m_hasValue; // 是否有值
    T m_value;       // 存储的值
};

#endif // OPTIONAL_H
