# ADMotion 日志系统完善总结

## 📋 完善概述

本次对 `admc.cpp` 和 `ioctrl.cpp` 中的函数进行了全面的日志系统完善，统一使用新的错误处理机制，提升了系统的可观测性和调试能力。

## 🔧 主要改进内容

### 1. admc.cpp 函数优化

#### 已优化的函数列表：
- `MC_AxisEnable` - 轴使能控制
- `MC_Stop` - 停止运动
- `MC_SetAxisPrm` - 设置轴参数
- `MC_RECORD_ZERO_POS` - 记录零位置
- `MC_ZeroPos` - 回零指令
- `MC_AxisHome` - 单轴回零
- `MC_AxisClearAlarm` - 清除轴报警
- `MC_GetErrorCode` - 获取错误代码
- `MC_GetPrfMode` - 获取轮廓模式
- `MC_CrdPrfTrap` - 设置坐标系梯形模式

#### 优化特点：
1. **统一错误处理**：使用 `LogAndReturnError` 处理参数验证错误
2. **智能SendCommand错误处理**：使用 `LogSendCommandError` 区分通讯错误和DSP错误
3. **详细上下文信息**：错误日志包含具体的参数值和操作上下文
4. **分级日志记录**：INFO级别记录操作开始，DEBUG级别记录详细参数，ERROR级别记录错误

### 2. ioctrl.cpp 函数优化

#### 已优化的函数列表：
- `MC_Open` - 打开连接
- `MC_Close` - 关闭连接
- `MC_SetDeviceOutput` - 设置设备输出
- `MC_GetDeviceOutput` - 获取设备输出
- `MC_GetDeviceInput` - 获取设备输入
- `MC_SetSpeedIOParam` - 设置高速IO参数
- `MC_SetSpeedIOState` - 设置高速IO状态
- `MC_SetIOPluseEnable` - 设置IO脉冲使能
- `MC_SetIOPluseState` - 设置IO脉冲状态

#### 优化特点：
1. **连接管理日志**：详细记录连接建立和断开过程
2. **IO操作日志**：记录输入输出操作的具体数值
3. **参数验证增强**：添加了更严格的参数验证和对应的错误日志
4. **指针安全检查**：对所有指针参数进行空值检查

## 📊 日志级别使用规范

### LOG_INFO - 操作信息
```cpp
g_Log(handle, LOG_INFO, "MC_AxisEnable: crd=%d, axis=%d, operate=%d", crd, axis, operate);
g_Log(handle, LOG_INFO, "MC_Open: 尝试连接到 %s:%d", ip, port);
```
**用途**：记录重要的操作开始，包含关键参数信息

### LOG_DEBUG - 调试信息
```cpp
g_Log(handle, LOG_DEBUG, "MC_SetDeviceOutput: 输出值=0x%08X", *deviceOutput);
g_Log(handle, LOG_DEBUG, "MC_GetErrorCode: Getting error code for crd=%d, axis=%d", crd, axis);
```
**用途**：记录详细的调试信息，通常包含具体的数值

### LOG_ERROR - 错误信息
```cpp
// 通过 LogAndReturnError 自动记录
return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_AxisEnable", 
                        "无效的轴号 %d", axis);

// 通过 LogSendCommandError 自动记录
return LogSendCommandError(handle, ret, "MC_AxisEnable", commandInfo);
```
**用途**：记录错误情况，包含错误码描述和详细上下文

### INTERNAL_LOG - 内部调试
```cpp
INTERNAL_LOG(handle, "MC_AxisEnable is success: crd=%d, axis=%d, operate=%d", crd, axis, operate);
INTERNAL_LOG(handle, "MC_SetSpeedIOParam: 成功设置高速IO参数");
```
**用途**：记录操作成功的详细信息，仅在调试版本中启用

## 🎯 错误处理模式

### 1. 参数验证错误
```cpp
if ((axis < 0) || (axis > MAX_AXIS)) {
    return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_AxisEnable", 
                            "无效的轴号 %d", axis);
}
```
**特点**：
- 立即返回，不执行后续操作
- 包含具体的无效参数值
- 使用中文描述便于理解

### 2. SendCommand错误处理
```cpp
short ret = SendCommand(handle, tick);
if(ret != CMD_SUCCESS) {
    CommandUninitial(handle, tick);
    char commandInfo[128];
    snprintf(commandInfo, sizeof(commandInfo), "crd=%d, axis=%d, operate=%d", crd, axis, operate);
    return LogSendCommandError(handle, ret, "MC_AxisEnable", commandInfo);
}
```
**特点**：
- 先清理资源（CommandUninitial）
- 构建命令上下文信息
- 智能区分通讯错误和DSP错误
- 使用不同的日志级别

### 3. 指针参数检查
```cpp
if (!deviceOutput) {
    return LogAndReturnError(handle, CMD_API_ERROR_POINTER, "MC_SetDeviceOutput", 
                            "输出参数指针为空");
}
```
**特点**：
- 防止空指针访问
- 明确指出哪个参数为空
- 提高系统稳定性

## 📈 改进效果

### 1. 可观测性提升
- **操作追踪**：每个重要操作都有对应的日志记录
- **参数可见**：关键参数值在日志中清晰可见
- **错误定位**：错误发生时能快速定位问题原因

### 2. 调试效率提升
- **统一格式**：所有日志使用统一的格式和命名规范
- **分级记录**：可以根据需要调整日志级别
- **上下文完整**：错误日志包含完整的操作上下文

### 3. 系统稳定性提升
- **参数验证**：更严格的参数检查防止异常
- **资源管理**：错误情况下正确清理资源
- **错误分类**：不同类型错误使用不同处理策略

## 🔄 使用建议

### 1. 日志级别设置
```cpp
// 开发调试时
API_SetLogLevel(handle, LOG_DEBUG);

// 生产环境
API_SetLogLevel(handle, LOG_INFO);

// 性能敏感场景
API_SetLogLevel(handle, LOG_ERROR);
```

### 2. 日志回调设置
```cpp
void MyLogCallback(LogLevel level, const char* message, void* userData) {
    const char* levelStr = (level == LOG_ERROR) ? "ERROR" : 
                          (level == LOG_WARNING) ? "WARN" : 
                          (level == LOG_INFO) ? "INFO" : "DEBUG";
    printf("[%s] %s\n", levelStr, message);
}

API_SetLogCallback(handle, MyLogCallback, nullptr);
```

### 3. 错误处理最佳实践
```cpp
// 调用API函数
short ret = MC_AxisEnable(handle, 0, 0, 1);
if (ret != CMD_SUCCESS) {
    // 错误信息已通过回调记录，这里只需处理业务逻辑
    printf("轴使能失败，错误码: %d\n", ret);
    
    // 可选：获取详细错误描述
    const wchar_t* errorDesc = GetMotionErrorString(ret);
    wprintf(L"错误描述: %ls\n", errorDesc);
    
    return ret;
}
```

## 📝 后续计划

1. **扩展到其他文件**：将日志完善扩展到项目中的其他源文件
2. **性能优化**：在高频调用的函数中优化日志性能
3. **日志分析工具**：开发日志分析和可视化工具
4. **文档完善**：更新API文档，说明新的日志机制

---

**总结**：通过系统性的日志完善，ADMotion项目的可维护性和调试效率得到了显著提升。统一的错误处理机制和详细的日志记录为开发者提供了强大的问题诊断能力。
