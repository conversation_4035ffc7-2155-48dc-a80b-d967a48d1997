﻿/**
 * @file ErrorCode_Motion.cpp
 * @brief ADMotion运动控制库错误码处理实现
 * @version 3.0 - 移除轮询函数，统一使用回调方式
 * @date 2024-12-19
 *
 * 本版本移除了MotionGetErrorStr和Servo_GetErrString函数
 * 统一使用g_Log回调机制和GetMotionErrorString函数
 */

#include "ErrorCode_Motion.h"
#include "motion_error_codes.h"
#include <string>
#include <map>

//=============================================================================
// 兼容性错误码枚举定义（保持向后兼容）
//=============================================================================
// 注意：为了避免宏展开冲突，这里使用不同的枚举名称
enum DSPERROR_COMPAT : int
{
    CMD_SUCCESS_COMPAT                     = 1,   // OK - 注意：保持原值以确保兼容性
    CMD_CRD_NUM_ERROR_COMPAT               = -1,  //坐标系编号错误
    CMD_AXIS_NUM_ERROR_COMPAT              = -2,  //轴号错误
    CMD_AXIS_DIR_ERROR_COMPAT              = -3,  //轴方向错误
    CMD_AXIS_ENABLE_ERROR_COMPAT           = -4,  //轴使能错误
    CMD_AXIS_POSITIVE_LIM_ERROR_COMPAT     = -7,  //轴正限位
    CMD_AXIS_NEGATIVE_LIM_ERROR_COMPAT     = -8,  //轴负限位
    CMD_DSP_ERROR_AXIS_RUN_COMPAT          = -9,  //关节轴运行错误
    CMD_DSP_ERROR_PRF_RUN_COMPAT           = -10, //坐标轴运行错误
    CMD_MOTION_TYPE_ERROR_COMPAT           = -11, //插补运动类型错误
    CMD_CIRCLE_PLANT_ERROR_COMPAT          = -12, //圆弧规划错误
    CMD_AXIS_ALARM_COMPAT                  = -13, //轴报警
    CMD_ORIGIN_FLAG_ERROR_COMPAT           = -40, //原点报错
    CMD_DSP_ERROR_LOAD_COMPAT              = -15, //数据加载错误
    CMD_DSP_ERROR_PARSE_COMPAT             = -16, //暂停错误
    CMD_DSP_ERROR_ADD_RESULT_COMPAT        = -17, //轨迹结束错误
    CMD_DSP_ERROR_PARAMETER_COMPAT         = -18, //参数错误
    CMD_DSP_ERROR_AXIS_DISENABLE_COMPAT    = -19, //轴未使能
    CMD_DSP_ERROR_AXIS_ENABLE_COMPAT       = -20, //轴使能
    CMD_DSP_ERROR_AXIS_MAP_COMPAT          = -21, //轴表错误
    CMD_DSP_ERROR_AXIS_STS_COMPAT          = -22, //轴状态错误
    CMD_DSP_ERROR_PRF_MODE_COMPAT          = -23, //轨迹类型错误
    CMD_DSP_ERROR_HOOK_COMPAT              = -24,
    CMD_DSP_ERROR_PRF_MODE_HOME_COMPAT     = -25,    //回零错误
    CMD_DSP_ERROR_UNKNOWN_COMPAT           = -26,    //未知错误
    CMD_DSP_ERROR_CRD_HOST_FIFO_RUN_COMPAT = -27,    //未用到
    CMD_DSP_ERROR_CRD_FIFO1_RUN_COMPAT     = -28,    //未用到
    CMD_DSP_ERROR_CRD_AXIS_MAP_SAME_COMPAT = -29,    //轴表设定重复
    CMD_DSP_ERROR_CRD_FIFO_OVERFLOW_COMPAT = -30     //未用到
};

//=============================================================================
// 轮询函数已被移除 - 统一使用回调方式
//=============================================================================

/**
 * 注意：MotionGetErrorStr 和 Servo_GetErrString 函数已被移除
 *
 * 替代方案：
 * 1. 使用 API_SetLogCallback 设置回调函数，错误会自动通过 g_Log 记录
 * 2. 直接调用 GetMotionErrorString 函数获取错误描述
 *
 * 示例代码：
 *
 * // 方法1：设置回调函数（推荐）
 * void MyLogCallback(LogLevel level, const char* message, void* userData) {
 *     printf("[%d] %s\n", level, message);
 * }
 * API_SetLogCallback(handle, MyLogCallback, nullptr);
 *
 * // 方法2：直接获取错误描述
 * const wchar_t* errorDesc = GetMotionErrorString(errorCode);
 * wprintf(L"错误描述: %ls\n", errorDesc);
 */

//=============================================================================
// 兼容性函数实现
//=============================================================================

/**
 * @brief 获取错误码对应的错误描述信息（兼容性函数）
 * @param ErrCode 错误码
 * @return 错误描述字符串
 * @note 此函数在adconfig.h中声明，保持向后兼容
 */
const wchar_t* GetErrorStr(int ErrCode) {
    return GetMotionErrorString(ErrCode);
}

