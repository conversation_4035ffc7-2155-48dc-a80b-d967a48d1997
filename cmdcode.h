﻿#pragma once

//#define GET_VERSION   0x1001 //版本信息
//#define CLOSE         0x1003 //
//#define SET_CARD_NUM  0x1004 //坐标系号
//#define GET_CARD_NUM  0x1005
//#define GET_CLOCK     0x1006
//#define GET_EXE_CYCLE 0x1007

#define SETDeviceOUTPUT 0x1000
#define GETDeviceOUTPUT 0x1001
#define GETDeviceINPUT  0x1002
#define GETSpeedIOState 0x1004	//�������
#define GETSpeedIOParam	0x1005	//��ȡ���
#define SENDSPEEDIOPOINT            0x1028
#define SENDSPEEDIOEnable           0x1029
#define SENDSPEEDIOClearPoint       0x1030

#define SET_PROFILE_CONFIG 0x1100
#define GET_PROFILE_CONFIG 0x1101
#define SET_AXIS_CONFIG    0x1102
#define GET_AXIS_CONFIG    0x1103
#define SET_CONTROL_CONFIG 0x1104
#define GET_CONTROL_CONFIG 0x1105
#define SET_STEP_CONFIG    0x1106
#define GET_STEP_CONFIG    0x1107
#define SET_COUNT_CONFIG   0x1108
#define GET_COUNT_CONFIG   0x1109
#define SET_DI_CONFIG      0x110a
#define GET_DI_CONFIG      0x110b
#define SET_DO_CONFIG      0x110c
#define GET_DO_CONFIG      0x110d
#define SET_DAC_CONFIG     0x110e
#define GET_DAC_CONFIG     0x110f
#define SET_ADC_CONFIG     0x1110
#define GET_ADC_CONFIG     0x1111
#define GET_RES_TABLE      0x1112
#define SET_RETAIN_DATA    0x1113
#define GET_RETAIN_DATA    0x1114

#define CTRL_MODE     0x1130
#define ALARM_ENABLE  0x1131
#define LMT_ENABLE    0x1132
#define SET_POS_ERR   0x1133
#define GET_POS_ERR   0x1134
#define SET_STOP_DEC  0x1135
#define GET_STOP_DEC  0x1136
#define PROFILE_SCALE 0x1137
#define ENC_SCALE     0x1138
#define SET_MTR_BIAS  0x1139
#define GET_MTR_BIAS  0x113a
#define SET_MTR_LMT   0x113b
#define GET_MTR_LMT   0x113c

#define ENC_SNS     0x1160
#define LMT_SNS     0x1161
#define GPI_SNS     0x1162
#define STEP_MODE   0x1163
#define ENC_SOURCE  0x1164
#define SET_STOP_IO 0x1165

#define RESET      0x1200
#define CLR_STS      0x1201
#define SET_AXIS_PRM 0x1202

#define SET_POS          0x1210
#define GET_POS          0x1211
#define SET_VEL          0x1212
#define GET_VEL          0x1213
#define RECORD_ZERO_POS  0x1214
#define ZERO_POS         0x1215
#define UPDATE           0x1216
#define AXIS_HOME        0x1219
#define AXIS_CLEAR_ALARM 0x1220
#define AXIS_GET_ERRORCODE  0x1221  //指令ID：获取错误代码 int GetErrorCode(int CMD=AXIS_GET_ERRORCODE,int crd,int Axis);返回{CMD=AXIS_GET_ERRORCODE,ErrorCode(int16_t)}

#define STOP              0x1217
#define GET_PROFILE_INFO  0x1218
#define PROFILE_INFO_POS  1
#define PROFILE_INFO_VEL  2
#define PROFILE_INFO_ACC  3
#define PROFILE_INFO_MODE 4

#define PRF_TRAP     0x1230
#define SET_TRAP_PRM 0x1231
#define GET_TRAP_PRM 0x1232

#define PRF_AxisTRAP     0x1233
#define SET_AxisTRAP_PRM 0x1234
#define AxisTRAP_UPDATE  0x1235

#define PRF_JOG     0x1250
#define SET_JOG_PRM 0x1251
#define GET_JOG_PRM 0x1252
#define JOGUPDATE   0x1253

#define AXIS_ENABLE    0x1300 //   ##
#define SET_SOFT_LIMIT 0x1301
#define GET_SOFT_LIMIT 0x1302
#define SET_AXIS_BAND  0x1303
#define GET_AXIS_BAND  0x1304
#define GET_AXIS_INFO  0x1305

#define AXIS_INFO_PRF_POS 1
#define AXIS_INFO_PRF_VEL 2
#define AXIS_INFO_PRF_ACC 3
#define AXIS_INFO_ENC_POS 4
#define AXIS_INFO_ENC_VEL 5
#define AXIS_INFO_ENC_ACC 6
#define AXIS_INFO_ERROR   7

#define SET_PID            0x1320
#define GET_PID            0x1321
#define SET_CONTROL_FILTER 0x1322
#define GET_CONTROL_FILTER 0x1323
#define GET_CONTROL_INFO   0x1324
#define SET_KVFF_FILTER    0x1325
#define GET_KVFF_FILTER    0x1326

#define SET_COUNT_POS             0x1340
#define GET_COUNT_INFO            0x1341
#define COUNT_INFO_ENCODER_POS    1
#define COUNT_INFO_ENCODER_VEL    2
#define COUNT_INFO_PULSE_POS      3
#define COUNT_INFO_PULSE_VEL      4
#define COUNT_INFO_ENCODER_REGIST 5
#define COUNT_INFO_PULSE_REGIST   6

#define HOME            0x1360
#define GET_HOME_PRM    0x1361
#define GET_HOME_STATUS 0x1362
#define HOME_INIT       0x1363
#define HOME_INDEX      0x1364
#define HOME_STOP       0x1365

#define SET_CRD_PRM 0x1500
#define GET_CRD_PRM 0x1501
#define CRD_CLEAR   0x1502
#define CRD_START   0x1503

#define GET_CRD_POS    0x1507
#define GET_CRD_STATUS 0x1505
#define SEND_CRD_DATA  0x1506

#define GET_CRD_SYN_VEL    0x1508
#define SET_CRD_ROTATE_PRM 0x1509
#define CRD_STOP           0x150a
#define CRD_PAUSE          0x150b
#define SET_CRD_ZERO_POS   0x150c

#define GET_DI               0x1700
#define GET_DI_REVERSE_COUNT 0x1701
#define SET_DI_REVERSE_COUNT 0x1702
#define GET_DI_RAW           0x1703

#define SET_DO             0x1720
#define SET_DO_BIT         0x1721
#define GET_DO             0x1722
#define SET_DO_BIT_REVERSE 0x1723
#define SET_DO_MASK        0x1724
#define SET_DO_BIT_PULSE   0x1725

#define SET_DAC_VALUE  0x1740
#define GET_DAC_VALUE  0x1741
#define GET_ADC        0x1742
#define GET_ADC_VALUE  0x1743
#define SET_ADC_FILTER 0x1744

#define SET_CAPTURE_MODE          0x1760
#define GET_CAPTURE_MODE          0x1761
#define GET_CAPTURE_STATUS        0x1762
#define SET_CAPTURE_SENSE         0x1763
#define SET_CAPTURE_OFFSET        0x1764
#define GET_CAPTURE_OFFSET        0x1765
#define SET_CAPTURE_COUNT         0x1766
#define GET_CAPTURE_COUNT         0x1767
#define SET_CAPTURE_REPEAT        0x1768
#define GET_CAPTURE_REPEAT_STATUS 0x1769
#define GET_CAPTURE_REPEAT_POS    0x176a
#define CLEAR_CAPTURE_STATUS      0x176b
#define SET_GOHOME_TYPE 0x1366
#define SET_WORKHOME    0x1367


#define SET_COMPARE_MODE  0x1780
#define GET_COMPARE_MODE  0x1781
#define GET_COMPARE_STS   0x1782
#define COMPARE_CLEAR     0x1783
#define SEND_COMPARE_DATA 0x1784
#define COMPARE_OPTION    0x1785
#define COMPARE_FORCE_OUT 0x1786

// CAN:
#define CMD_ETHERNET_RESTART_OPEM 0x1017 //自动重连开关

//2D比较及高速IO控制参数
#define CMD_2D_COMP_ENABLE  		0x1020
#define CMD_2D_COMP_CLEAR  			0x1021
#define CMD_2D_COMP_SEND_DATA		0x1022
#define CMD_2D_COMP_SET_TYPE  		0x1023
#define CMD_2D_COMP_OUTP            0x1024


#define IOCMD_SetIOPluseEnable  0x1025
#define IOCMD_SetIOPluseState   0x1026
#define IOCMD_SetIOPluseTrigger 0x1027
#define IOCMD_GetFpgaVersion    0x1030

