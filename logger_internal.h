#pragma once
#ifndef LOGGER_INTERNAL_H
#define LOGGER_INTERNAL_H

#include "logger_proxy.h" // For g_Log and LogLevel

// This macro controls the developer-level/internal logging.
// It should be defined in the compiler settings for special debug builds only.
// In standard release builds, this macro should NOT be defined.
#ifdef ENABLE_INTERNAL_LOGGING
    // When enabled, internal logs are routed to g_Log with DEBUG level.
    #define INTERNAL_LOG(handle, format, ...) g_Log(handle, LOG_DEBUG, format, ##__VA_ARGS__)
#else
    // When disabled, the macro compiles to nothing, incurring zero performance cost.
    #define INTERNAL_LOG(handle, format, ...) ((void)0)
#endif

#endif // LOGGER_INTERNAL_H 