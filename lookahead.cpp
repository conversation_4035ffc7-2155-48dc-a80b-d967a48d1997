﻿//#include <Windows.h>
#include <string.h>
#include "math.h"
//#include "config.h"添加到crd.h头文件中了
#include "card.h"
#include "crdmath.h"
#include "ADMotionPackage.h"
#include "CardData.h"
#include "logger_proxy.h"

short MC_insert_new_line_UnLookAhead(TADMotionConn* handle, short crd, TCrdData* pCrdData)
{
    if (pCrdData->motionType == CRD_CMD_TYPE_G0LINE)
    {
        pCrdData->motionType = CRD_CMD_TYPE_LINE;
    }
    return SendCrdData(handle, crd, pCrdData);
}

short MC_insert_line_Processing(TADMotionConn* handle, short crd, TCrdData* pCrdData)
{
    TCardData* pCard = handle->CardData;

    if (pCard->gLookAheadPrm[crd].enable == false)
    {
        if (pCrdData == nullptr)
        {
            return CMD_SUCCESS;
        }
        else
        {
            if (pCrdData->motionType == CRD_CMD_TYPE_G0LINE)
            {
                pCrdData->motionType = CRD_CMD_TYPE_LINE;
            }
            //前瞻未开启，把当前输入数据输出
            return SendCrdData(handle, crd, pCrdData);
        }
    }
    //前瞻所有段后，把所有数据一次性压入dsp
    if (pCrdData == nullptr)
    {
        pCard->gLookAheadPrm[crd].endflag = true;
        LookAheadHandle(handle, crd,  &pCard->gLookAheadPrm[crd]);
        return CMD_SUCCESS;
    }
    // 计算当前指针位置
    pCard->gLookAheadPrm[crd].pCmdBufInPtr =
        pCard->gLookAheadPrm[crd].pHostData + pCard->gLookAheadPrm[crd].nBufRear;
    TLookAheadPrm* pLookAheadprm=&pCard->gLookAheadPrm[crd];
    memcpy(pLookAheadprm->pCmdBufInPtr, pCrdData, sizeof(TCrdData));
    //指针后移一位
    pCard->gLookAheadPrm[crd].nBufRear =
        (pCard->gLookAheadPrm[crd].nBufRear + 1) % pCard->gLookAheadPrm[crd].count; //循环自加
    pCard->gLookAheadPrm[crd].nBufCount += 1;

    //进入前瞻流程
    LookAheadHandle(handle, crd, &pCard->gLookAheadPrm[crd]);
    return CMD_SUCCESS;
}

//发送数据到插补缓冲区

short InsertNewLine(TADMotionConn* handle, short crd, TCrdData* pCrdData)
{
    short      crdIndex = crd - 1;
    TCardData* pCard    = handle->CardData;
    //非前瞻数据 调用MC_insert_new_line_UnLookAhead函数。
    if (pCard->gLookAheadPrm[crdIndex].enable == false)
    {
        if (pCrdData == nullptr)
        {
            return CMD_SUCCESS;
        }
        else
        {
            if (pCrdData->motionType == CRD_CMD_TYPE_G0LINE)
            {
                pCrdData->motionType = CRD_CMD_TYPE_LINE;
            }
            //前瞻未开启，把当前输入数据输出
            return SendCrdData(handle, crd, pCrdData);
        }
    }
    //前瞻最后一段NULL发送，所有前瞻数据输出
    if (pCrdData == nullptr)
    {
        pCard->gLookAheadPrm[crdIndex].endflag = true;
        LookAheadHandle(handle, crd,  &pCard->gLookAheadPrm[crdIndex]);
        return CMD_SUCCESS;
    }
    // Buffer满返回错误
    if ((pCard->gLookAheadPrm[crdIndex].nBufCount == pCard->gLookAheadPrm[crdIndex].count)
        && (pCard->gLookAheadPrm[crdIndex].nBufCount != 0))
    {
        return CMD_API_ERROR_CRD_FIFO_FULL;
    }
    pCard->gLookAheadPrm[crdIndex].pCmdBufInPtr =
        pCard->gLookAheadPrm[crdIndex].pHostData + pCard->gLookAheadPrm[crdIndex].nBufRear;
    memcpy(pCard->gLookAheadPrm[crdIndex].pCmdBufInPtr, pCrdData, sizeof(TCrdData));
    //指针后移一位
    pCard->gLookAheadPrm[crdIndex].nBufRear =
        (pCard->gLookAheadPrm[crdIndex].nBufRear + 1) % pCard->gLookAheadPrm[crdIndex].count;
    pCard->gLookAheadPrm[crdIndex].nBufCount += 1;
    //进入前瞻流程
    LookAheadHandle(handle, crd, &pCard->gLookAheadPrm[crdIndex]);

    return CMD_SUCCESS;
}

//当前插入前瞻buf数量
short GetBuffCount(TADMotionConn* handle, short crd)
{
    TCardData* pCard    = handle->CardData;
    short      crdIndex = crd;
    return pCard->gLookAheadPrm[crdIndex].nBufCount;
}

//当前FIFO数据是否满
short IsBuffFull(TADMotionConn* handle, short crd)
{
    TCardData* pCard    = handle->CardData;
    short      crdIndex = crd - 1;
    if (pCard->gLookAheadPrm[crdIndex].nBufCount == pCard->gLookAheadPrm[crdIndex].count)
    {
        return true;
    }
    return false;
}
//判断buffer是否空
short IsBuffEmpty(TADMotionConn* handle, short crd)
{
    short      crdIndex = crd - 1;
    TCardData* pCard    = handle->CardData;
    if (pCard->gLookAheadPrm[crdIndex].nBufCount == 0)
    {
        return true;
    }
    return false;
}

//对连续小线段缓冲和变量进行初始化
short InitLookAheadBuff(TADMotionConn* handle, short crd)
{
    if (!handle) {
        g_Log(handle, LOG_ERROR, "InitLookAheadBuff: handle is null");
        return CMD_API_ERROR_POINTER;
    }

    if (!handle->CardData) {
        g_Log(handle, LOG_ERROR, "InitLookAheadBuff: CardData is null");
        return CMD_API_ERROR_POINTER;
    }

    if (crd < 0 || crd >= MAX_CRD) {
        g_Log(handle, LOG_ERROR, "InitLookAheadBuff: Invalid crd: %d", crd);
        return CMD_API_ERROR_OUT_RANGE;
    }



    TCardData* pCard = handle->CardData;
    TLookAheadPrm* pLookAheadPrm = &pCard->gLookAheadPrm[crd];

    try {
        // 确保缓冲区已创建
        if (!pLookAheadPrm->buffer) {
            pLookAheadPrm->buffer = std::make_shared<LookAheadBuffer>(pLookAheadPrm->count > 0 ? pLookAheadPrm->count : 200);
            g_Log(handle, LOG_INFO, "InitLookAheadBuff: Created new buffer for crd=%d", crd);
        }

        // 使用新的initialize方法初始化
        pLookAheadPrm->initialize();

        g_Log(handle, LOG_INFO, "InitLookAheadBuff: Successfully initialized buffer for crd=%d", crd);
        return CMD_SUCCESS;
    } catch (const std::exception& e) {
        g_Log(handle, LOG_ERROR, "InitLookAheadBuff: Exception: %s", e.what());
        return CMD_API_ERROR_POINTER;
    }
}
short MC_CloseLookAhead(TADMotionConn* handle, short crd)
{
    if (!handle) {
        g_Log(handle, LOG_ERROR, "MC_CloseLookAhead: handle is null");
        return CMD_API_ERROR_POINTER;
    }

    if (!handle->CardData) {
        g_Log(handle, LOG_ERROR, "MC_CloseLookAhead: CardData is null");
        return CMD_API_ERROR_POINTER;
    }

    if (crd < 0 || crd >= MAX_CRD) {
        g_Log(handle, LOG_ERROR, "MC_CloseLookAhead: Invalid crd: %d", crd);
        return CMD_API_ERROR_OUT_RANGE;
    }

 

    short rtn = CrdDataProcess(handle, crd, nullptr);
    if (CMD_SUCCESS != rtn) {
        g_Log(handle, LOG_ERROR, "MC_CloseLookAhead: CrdDataProcess failed with error: %d", rtn);
        return rtn;
    }

    g_Log(handle, LOG_INFO, "MC_CloseLookAhead: Successfully closed look ahead for crd=%d", crd);
    return CMD_SUCCESS;
}

short MC_CloseLookAheadTab(TADMotionConn* handle, short crd)
{
    if (!handle) {
        g_Log(handle, LOG_ERROR, "MC_CloseLookAheadTab: handle is null");
        return CMD_API_ERROR_POINTER;
    }

    if (!handle->CardData) {
        g_Log(handle, LOG_ERROR, "MC_CloseLookAheadTab: CardData is null");
        return CMD_API_ERROR_POINTER;
    }

    if (crd < 0 || crd >= MAX_CRD) {
        g_Log(handle, LOG_ERROR, "MC_CloseLookAheadTab: Invalid crd: %d", crd);
        return CMD_API_ERROR_OUT_RANGE;
    }

 

    short rtn = CrdDataProcess(handle, crd, nullptr);
    if (CMD_SUCCESS != rtn) {
        g_Log(handle, LOG_ERROR, "MC_CloseLookAheadTab: CrdDataProcess failed with error: %d", rtn);
        return rtn;
    }

    TCardData* pCard = handle->CardData;
    TLookAheadPrm* pLookAheadPrm = &pCard->gLookAheadPrm[crd];

    // 禁用前瞻
    pLookAheadPrm->enable = false;

    // 重置缓冲区状态
    try {
        if (pLookAheadPrm->buffer) {
            pLookAheadPrm->reset();
            g_Log(handle, LOG_INFO, "MC_CloseLookAheadTab: Reset buffer for crd=%d", crd);
        }
    } catch (const std::exception& e) {
        g_Log(handle, LOG_ERROR, "MC_CloseLookAheadTab: Exception during buffer reset: %s", e.what());
        // 继续执行，不返回错误
    }

    g_Log(handle, LOG_INFO, "MC_CloseLookAheadTab: Successfully closed look ahead tab for crd=%d", crd);
    return CMD_SUCCESS;
}
//前瞻处理程序

short LookAheadHandle(TADMotionConn* handle, short crd, TLookAheadPrm* pLookAheadPrm)
{
    auto pCard = handle->CardData;
    auto crdIndex = crd;
    auto head = pLookAheadPrm->pCmdBufOutPtr; //获得数据读取指针，当前第一个输出的数据头指针
    auto p = pLookAheadPrm->pCmdBufOutPtr; //获得数据读取指针，当前第一个输出的数据头指针
    short rtn = 0;
    short i = 0, j = 0, k = 0; // 初始化变量

    //第一段初速度
    auto initialSpeed = pLookAheadPrm->lookAheadInitVel;
    double endVel; //第一段末速度
    double tempVel;

    //当前buf数量
    short len = GetBuffCount(handle, crd);
    //轴最大加速度
    double axisAccMax = pLookAheadPrm->accMax;
    double lengthMin  = 50 * (axisAccMax / 32); //实验用参数，加速度到达50个周期内是小线段
    //计算上一段末速度
    LookAheadPreProcess(handle, pLookAheadPrm, len, axisAccMax);

    if (len == pLookAheadPrm->count)
    {                                                                                  //前瞻一段
        if (IsIntpType(head->motionType) && (head->motionType != CRD_CMD_TYPE_BEZIER)) //条件判断用,当前段非bezier
        {
            endVel  = head->velEnd; //第一段末速度
            tempVel = sqrt(initialSpeed * initialSpeed + 2 * head->acc * (head->length));
            if (endVel >= tempVel)
            {
                endVel = tempVel;
                double beforeSpeed;
                double afterSpeed;
                //p->velEndAdjust = p->velEnd; //第一段调整速度初始化为p->velEnd
                p->velEndAdjust = endVel;
                beforeSpeed     = p->velEndAdjust;
                //正向调整加速到第N段
                for (j = 0; j < len - 1; j++)
                { //正向前进一段
                    //p = PointerMove(p, 1, pLookAheadPrm);
                    if (IsIntpType(p->motionType) && (p->motionType != CRD_CMD_TYPE_BEZIER))
                    { //当前是插补数据非bezier曲线并运动
                        if (p->velEnd > beforeSpeed)
                        {
                            double length = p->velEnd * p->velEnd - beforeSpeed * beforeSpeed;
                            double as2    = 2 * p->acc * p->length;
                            if (length > as2)
                            {
                                p->velEndAdjust = sqrt(beforeSpeed * beforeSpeed + as2);
                            }
                            else
                            {
                                p->velEndAdjust = p->velEnd; //调整速度为p->velEnd
                            }
                        }
                        else
                        {
                            p->velEndAdjust = p->velEnd; //调整速度为p->velEnd
                        }
                        beforeSpeed = p->velEndAdjust;
                    }
                    p = PointerMove(p, 1, pLookAheadPrm);
                }
                afterSpeed = 0; //末速度第一段从0开始反向加速
                double acc;     //保存后一段的加速度和长度
                double length;
                if (IsIntpType(p->motionType) && (p->motionType != CRD_CMD_TYPE_BEZIER))
                {
                    acc    = p->acc;
                    length = p->length;
                }
                else
                {
                    acc = 0;
                    length = p->length;
                    return CMD_API_ERROR_TYPE;
                }
                //反向调整加速到第1段。
                for (i = j; i > 0; i--)
                {
                    p = PointerMove(p, -1, pLookAheadPrm);
                    //当前为插补数据
                    if (IsIntpType(p->motionType) && (p->motionType != CRD_CMD_TYPE_BEZIER))
                    {
                        if (p->velEnd > afterSpeed)
                        {
                            double len = p->velEnd * p->velEnd - afterSpeed * afterSpeed;
                            double as2 = 2 * acc * length;
                            if (len > as2)
                            {
                                p->velEndAdjust = sqrt(afterSpeed * afterSpeed + as2);
                            }
                        }
                        acc        = p->acc;
                        length     = p->length;
                        afterSpeed = p->velEndAdjust;
                    }
                    else
                    {
                        return CMD_API_ERROR_TYPE;
                    }
                }
                if (endVel > p->velEndAdjust)
                {
                    head->velEnd = p->velEndAdjust;
                }
                else
                {
                    head->velEnd = endVel;
                }
                // TODO:连续小线段用户需要输入小线段的最大长度
                ////如果速度曲线过小
                if (head->length < lengthMin)
                {
                    double maxVel =
                        sqrt(initialSpeed * initialSpeed + head->velEnd * head->velEnd + 2 * head->acc * (head->length));
                    if ((maxVel > initialSpeed) && (maxVel > head->velEnd))
                    {
                        double T         = ((maxVel - initialSpeed) + (maxVel - head->velEnd)) / head->acc;
                        double decLength = (initialSpeed + head->velEnd) / 2 * T;
                        if ((decLength / head->length) > 0.9)
                        {
                            // head->vel = max(head->velEnd, initialSpeed);
                        }
                    }
                    //第二段末速度//读取后一个有插补数据的插补段数据motionType<5
                    p           = PointerMove(p, 1, pLookAheadPrm);
                    short index = len - 1;
                    while ((!IsIntpType(p->motionType) || (p->motionType == CRD_CMD_TYPE_BEZIER)) && (index > 1))
                    {
                        index--;
                        p = PointerMove(p, 1, pLookAheadPrm);
                    }
                    if (IsIntpType(p->motionType) && (p->motionType != CRD_CMD_TYPE_BEZIER))
                    { //小线段控制段前和断后的速度波动误差
                        double tempV1, tempV2;
                        tempV1 = head->velEnd - initialSpeed;
                        tempV2 = head->velEnd - p->velEnd;
                        if ((tempV1 > 0) && (tempV2 > 0) && (head->velEnd > 0))

                        {
                            if ((tempV1 <= tempV2) && (tempV1 / head->velEnd < 0.1))
                            {
                                head->vel    = initialSpeed;
                                head->velEnd = head->vel;
                            }
                            else if ((tempV1 > tempV2) && (tempV2 / head->velEnd < 0.1))
                            {
                                head->vel    = p->velEnd;
                                head->velEnd = head->vel;
                            }
                        }
                    }
                }
            }
            else if (head->motionType == CRD_CMD_TYPE_BEZIER)
            {
                head->velEnd = pLookAheadPrm->lookAheadInitVel;
            }
            //发送一条插补数据到下位机
            short rtn = 0;

            pCard->gLookAheadPrm[crdIndex].pCmdBufOutPtr->velStart = initialSpeed; // TODO

            rtn = SendCrdData(handle, pLookAheadPrm->crd, pCard->gLookAheadPrm[crdIndex].pCmdBufOutPtr);
            rtn = CMD_SUCCESS;
            if (CMD_SUCCESS != rtn)
            {
                return rtn;
            }
            pCard->gLookAheadPrm[crdIndex].nBufHead =
                (pCard->gLookAheadPrm[crdIndex].nBufHead + 1) % pCard->gLookAheadPrm[crdIndex].count;
            pCard->gLookAheadPrm[crdIndex].pCmdBufOutPtr =
                pCard->gLookAheadPrm[crdIndex].pHostData + pCard->gLookAheadPrm[crdIndex].nBufHead;
            pCard->gLookAheadPrm[crdIndex].nBufCount -= 1;
            pLookAheadPrm->lookAheadInitVel = head->velEnd;
        }
    }
    else if (pLookAheadPrm->endflag == true)
    { //前瞻所有段然后输出所有段。
        double beforeSpeed = initialSpeed;
        p->velStart        = initialSpeed;
        if (IsIntpType(p->motionType) && (p->motionType != CRD_CMD_TYPE_BEZIER)) //条件判断用,当前段非bezier
        {
            endVel  = p->velEnd; //第一段末速度
            tempVel = sqrt(initialSpeed * initialSpeed + 2 * p->acc * (p->length));
            if (endVel >= tempVel)
            {
                p->velEnd   = tempVel;
                beforeSpeed = tempVel;
            }
            beforeSpeed = p->velEnd; // s
        }
        //正向调整加速到第N段
        int j_count = 0; // 记录循环次数
        for (int j = 0; j < len - 1; j++)
        {
            j_count = j; // 记录当前循环次数
            p           = PointerMove(p, 1, pLookAheadPrm);
            p->velStart = beforeSpeed; // s
            //当前为插补数据
            if (IsIntpType(p->motionType) && (p->motionType != CRD_CMD_TYPE_BEZIER))
            {
                if (p->velEnd > beforeSpeed)
                {
                    double length = p->velEnd * p->velEnd - beforeSpeed * beforeSpeed;
                    double as2    = 2 * p->acc * p->length;
                    if (length > as2)
                    {
                        p->velEnd = sqrt(beforeSpeed * beforeSpeed + as2);
                    }
                }
                beforeSpeed = p->velEnd;
            }
        }
        double afterSpeed = 0;
        double acc; //反向调整后一段加速度和长度
        double length;
        if (IsIntpType(p->motionType) && (p->motionType != CRD_CMD_TYPE_BEZIER))
        {
            acc       = p->acc;
            length    = p->length;
            p->velEnd = 0;
        }
        else
        {//
           return CMD_API_ERROR_TYPE; //不会发生该情况，如执行到则严重错误
        }
        ////最后一段插补数据末速度为0.
        pLookAheadPrm->pCmdBufInPtr->velEnd = 0;
        //反向调整加速到第1段
        TCrdData* pTemp = p; // s
        for (i = j_count; i > 0; i--)
        {
            p = PointerMove(p, -1, pLookAheadPrm);
            //当前为插补数据
            if (IsIntpType(p->motionType) && (p->motionType != CRD_CMD_TYPE_BEZIER))
            {
                if (p->velEnd > afterSpeed)
                {
                    double len2 = p->velEnd * p->velEnd - afterSpeed * afterSpeed;
                    double as2  = 2 * acc * length;
                    if (len2 > as2)
                    {
                        p->velEnd = sqrt(afterSpeed * afterSpeed + as2);
                    }
                }
                acc             = p->acc;
                length          = p->length;
                afterSpeed      = p->velEnd;
                pTemp->velStart = afterSpeed;
                pTemp           = p;
            }
        }

        for (k = 0; k < len - 2; k++)
        { //如果速度曲线过小
            if (IsIntpType(p->motionType) && (p->motionType != CRD_CMD_TYPE_BEZIER)
                && (p->length < lengthMin)) //曲线长度小于10000个脉冲
            { //小线段如果要往上加速的速度少，避免频繁起降，不超端点速度。
                double maxVel = sqrt(initialSpeed * initialSpeed + p->velEnd * p->velEnd + 2 * p->acc * (p->length));
                if ((maxVel > initialSpeed) && (maxVel > p->velEnd))
                {
                    double T         = ((maxVel - initialSpeed) + (maxVel - p->velEnd)) / p->acc;
                    double decLength = (initialSpeed + p->velEnd) / 2 * T;
                    if (decLength / p->length > 0.9)
                    {
                        // p->vel = max(p->velEnd, initialSpeed);
                    }
                }
                double tempV1, tempV2;
                //小线段控制段前和断后的速度波动误差
                double lastSpeed = p->velEnd;
                tempV1           = lastSpeed - initialSpeed;
                //第二段末速度
                TCrdData* p2    = PointerMove(p, 1, pLookAheadPrm);
                short     count = k + 1;
                while ((!IsIntpType(p2->motionType) || (p2->motionType == CRD_CMD_TYPE_BEZIER)) && (count < len - 2))
                {
                    count++;
                    p2 = PointerMove(p2, 1, pLookAheadPrm);
                }
                if (IsIntpType(p2->motionType) && (p2->motionType != CRD_CMD_TYPE_BEZIER))
                {
                    tempV2 = lastSpeed - p2->velEnd;
                    if ((tempV1 > 0) && (tempV2 > 0) && (lastSpeed > 0))
                    {
                        if ((tempV1 <= tempV2) && (tempV1 / lastSpeed < 0.1))
                        {
                            p->vel    = initialSpeed;
                            p->velEnd = p->vel;
                        }
                        else if ((tempV1 > tempV2) && (tempV2 / lastSpeed < 0.1))
                        {
                            p->vel    = p->velEnd;
                            p->velEnd = p->vel;
                        }
                    }
                }
            }
            if (IsIntpType(p->motionType) && (p->motionType != CRD_CMD_TYPE_BEZIER))
            {
                initialSpeed = p->velEnd;
            }
            p           = PointerMove(p, 1, pLookAheadPrm);
            p->velStart = initialSpeed; // TODO
        }

        for (i = 0; i < len; i++)
        { //发送一条插补数据到下位机
            rtn = SendCrdData(handle, pLookAheadPrm->crd, pCard->gLookAheadPrm[crdIndex].pCmdBufOutPtr);
            rtn = CMD_SUCCESS;
            if (CMD_SUCCESS != rtn)
            {
                return rtn;
            }
            pCard->gLookAheadPrm[crdIndex].nBufHead =
                (pCard->gLookAheadPrm[crdIndex].nBufHead + 1) % pCard->gLookAheadPrm[crdIndex].count;
            pCard->gLookAheadPrm[crdIndex].pCmdBufOutPtr =
                pCard->gLookAheadPrm[crdIndex].pHostData + pCard->gLookAheadPrm[crdIndex].nBufHead;
            pCard->gLookAheadPrm[crdIndex].nBufCount -= 1;
        }
        pLookAheadPrm->lookAheadInitVel = 0;
        pLookAheadPrm->endflag          = false;
    }
    return CMD_SUCCESS;
}

//前瞻预处理
void LookAheadPreProcess(TADMotionConn* handle, TLookAheadPrm* pdata, short len, double accMax)
{
    auto rear = pdata->pCmdBufInPtr; //新数据插入的位置,尾指针。
    double speed;
    double acc;
    //当前插入数据段是有插补数据的
    if (IsIntpType(rear->motionType) && (CRD_CMD_TYPE_JUMP != rear->motionType))
    { //初始化末速度等于目标速度
        rear->velEnd = rear->vel;
        if (len > 1)
        { //读取前一个插入的插补数据
            TCrdData* lastp = PointerMove(rear, -1, pdata);
            short     index = len - 1;
            //读取前一个有插补数据的插补段数据 motionType<5

            while (!IsIntpType(lastp->motionType) && (index > 1))
            {
                index--;
                lastp = PointerMove(lastp, -1, pdata);
            }
            if (IsIntpType(lastp->motionType))
            {
                if (rear->motionType == CRD_CMD_TYPE_BEZIER) //当前段是bezier
                {
                    rear->velEnd = lastp->vel;
                    rear->vel    = lastp->vel;
                }
                else if (lastp->motionType == CRD_CMD_TYPE_G0LINE)
                {
                    lastp->motionType = CRD_CMD_TYPE_LINE;
                    lastp->velEnd     = 0;
                }
                else if (lastp->motionType != CRD_CMD_TYPE_BEZIER) //上一段不是bezier转接段,当前段不是bezier
                {                                                  //前面插补BUF不含有插补数据
                    if (lastp->vel > rear->vel)
                    {
                        speed = rear->vel;
                    }
                    else
                    {
                        speed = lastp->vel;
                    }
                    if (lastp->acc > rear->acc)
                    {
                        acc = rear->acc;
                    }
                    else
                    {
                        acc = lastp->acc;
                    }
                    double firstVector[3];  // 第一段终点切向量
                    double secondVector[3]; // 第二段终点切向量
                    //第一段直线转直线
                    for (auto i = 0; i < 3; i++)
                    {
                        secondVector[i] = rear->startVector[i];
                    }

                    //第二段直线转直线
                    if ((lastp->motionType == CRD_CMD_TYPE_LINE) || (lastp->motionType == CRD_CMD_TYPE_G0LINE)
                        || (lastp->motionType == CRD_CMD_TYPE_FIVEAXIS))
                    {
                        for (auto i = 0; i < 3; i++)
                        {
                            firstVector[i] = lastp->startVector[i];
                        }
                    }
                    else //圆弧和其他空间圆曲线
                    {
                        for (auto i = 0; i < 3; i++)
                        {
                            firstVector[i] = lastp->endVector[i];
                        }
                    }
                    //计算夹角速度限制
                    double productor =
                        firstVector[0] * secondVector[0] + firstVector[1] * secondVector[1] + firstVector[2] * secondVector[2];
                    if (productor < -1)
                    {
                        productor = -1;
                    }
                    if (productor > 1)
                    {
                        productor = 1;
                    }
                    double angle = acos(productor);
                    if (angle > 0.001)
                    { //计算最大角速度 V <= T＊a／(2*sin(angle/2）)
                        // speed = 0.25*acc * 0.5 / sin(angle / 2);
                        speed = accMax * 0.5 / sin(angle / 2);
                    }
                    if(angle > (3.14159*0.95))
                    {
                        speed = 0;
                    }
                    speed = MinSpeed(speed, lastp->vel, rear->vel);

                    if (lastp->velEnd > speed)
                    {
                        lastp->velEnd = speed;
                        //lastp->vel = speed;  //Todo
                    }
                }
            }
        }
    }
    else if (rear->motionType == CRD_CMD_TYPE_JUMP)
    {
        if (len > 1)
        {
            TCrdData* lastp = PointerMove(rear, -1, pdata);
            short     index = len - 1;
            //读取前一个有插补数据的插补段数据 motionType<5(并且不是bezier转接段)
            while (!IsIntpType(lastp->motionType) && index > 1)
            {
                index--;
                lastp = PointerMove(lastp, -1, pdata);
            }
            if (IsIntpType(lastp->motionType))
            {
                lastp->velEnd = 0;
            }
        }
    }
    else if (rear->motionType == CRD_CMD_TYPE_BUFDATA)
    {
        if (len > 1)
        {
            if ((rear->bufData.delayData.type == CRD_BUFDATA_BUFDELAY)
                || ((rear->bufData.motionData.type == CRD_BUFDATA_BUFMOTION)
                    && (rear->bufData.motionData.subType == CRD_BUFDATA_SUB_TYPE_MOVE)
                    && (0 != rear->bufData.motionData.model)))
            {
                TCrdData* lastp = PointerMove(rear, -1, pdata);
                short     index = len - 1;
                //读取前一个有插补数据的插补段数据 motionType<5(并且不是bezier转接段)
                while (!IsIntpType(lastp->motionType) && index > 1)
                {
                    index--;
                    lastp = PointerMove(lastp, -1, pdata);
                }
                if (IsIntpType(lastp->motionType))
                {
                    lastp->velEnd = 0;
                }
            }
        }
    }
}

//指针上移，下移n位
TCrdData* PointerMove(TCrdData* pSrc0, int32_t n, TLookAheadPrm* pLookAheadPrm)
{
    if (!pLookAheadPrm) {
        // Since handle is not available here, we cannot use g_Log.
        // This indicates a design issue where logging depends on a handle that isn't always present.
        // For now, we'll have to omit this log message.
        // LOG_ERROR("PointerMove: pLookAheadPrm is null");
        return nullptr;
    }

    if (!pSrc0) {
        // Same issue as above, no handle.
        // LOG_ERROR("PointerMove: pSrc0 is null");
        return nullptr;
    }

    try {
        if (pLookAheadPrm->buffer) {
            return pLookAheadPrm->buffer->movePointer(pSrc0, n);
        }
    }
    catch (const std::exception& e) {
        // No handle available
        // LOG_ERROR("PointerMove: Exception in buffer->movePointer: " + std::string(e.what()));
    }
    
    // Fallback to legacy pointer movement
    // LOG_WARNING("PointerMove: Falling back to legacy pointer movement");
    
    if (!pLookAheadPrm->buffer && !pLookAheadPrm->pHostData) {
        // LOG_ERROR("PointerMove: Cannot fall back, invalid buffer state");
        return nullptr;
    }

    TCrdData* buffer_start = pLookAheadPrm->pHostData;
    int buffer_size = pLookAheadPrm->count;
    
    if (!buffer_start || buffer_size <= 0) {
        // LOG_ERROR("PointerMove: Invalid buffer state (no buffer and no pHostData)");
        return nullptr;
    }

    // Check if the source pointer is within the buffer's bounds
    if (pSrc0 < buffer_start || pSrc0 >= buffer_start + buffer_size) {
        // LOG_WARNING("PointerMove: Source pointer is outside buffer range, resetting to buffer start");
        pSrc0 = buffer_start;
    }

    intptr_t offset = pSrc0 - buffer_start;
    intptr_t new_offset = (offset + n) % buffer_size;
    if (new_offset < 0) {
        new_offset += buffer_size;
    }

    return buffer_start + new_offset;
}
