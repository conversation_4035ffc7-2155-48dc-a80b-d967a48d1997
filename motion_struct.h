#ifndef MOTION_STRUCT_H
#define MOTION_STRUCT_H


#include "ADOSType.h"

/**
 * @brief 点位运动参数结构体
 */
struct TTrapPrm {
    double acc;    // 加速度:pulse/ms^2
    double velMax; // 起跳速度:pulse/ms
    short  rat;    // 倍率
    double StartPos[2];
    double posTarget[2];
};


/**
 * @brief JOG运动参数结构体
 */
struct TJogPrm {
    int32_t acc;    // 加速度:pulse/ms^2
    int32_t dec;    // 减速度:pulse/ms^2
    int32_t Maxvel; // 起跳速度:pulse/ms
    int32_t rate;   // 倍率
};


/**
 * @brief 坐标系参数结构体
 */
struct TCrdPrm {
    double  synVelMax;     // 坐标系内最大合成速度
    double  synAccMax;     // 坐标系内最大合成加速度
    short   flagIsZero;
    int32_t originPos[5];  // originPos[0],[1],[2],[3],[4]:分别代表X,Y,Z,A,C轴的原点位置

    short   axisMap[5];    // axisMap[0],[1],[2],[3],[4]:分别代表X,Y,Z,A,C轴号。若值为-1则表示当前轴没有映射
    double  axisVelMax[5]; // axisVelMax[0],[1],[2],[3],[4]:分别代表X,Y,Z,A,C轴的最大速度。
    double  decSmoothStop; // 坐标系内平滑停止减速度
    double  decAbruptStop; // 坐标系内紧急停止减速度
    short   setOriginFlag; // 设置原点坐标值标志,0:默认当前规划位置为原点位置;1:用户指定原点位置
};

/**
 * @brief 速度IO点结构体
 * isOpen:是否开阀   openStyle：开阀方式  x/y：胶阀目标点
 */
struct SpeedIOPoint {
    double  x;
    double  y;
    short isOpen;
    short openStyle;
};


#endif // MOTION_STRUCT_H
