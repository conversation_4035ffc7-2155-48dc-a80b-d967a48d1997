#include "logger_proxy.h"
#include "motion_conn.h" // For MotionConn class
#include "motion_error_codes.h" // For error code definitions and GetMotionErrorString
#include <map>
#include <mutex>
#include <memory>
#include <cstdarg> // For va_list, va_start, va_end
#include <string> // For std::string

// 从admc_pci.cpp中外部引用全局变量
extern std::map<TADMotionConn*, std::shared_ptr<MotionConn>> g_handleMap;
extern std::mutex g_handleMapMutex;

void g_Log(TADMotionConn* handle, LogLevel level, const char* format, ...) {
    if (!handle) {
        return; // 如果句柄为空，则不记录
    }

    std::shared_ptr<MotionConn> connWrapper = nullptr;
    {
        std::lock_guard<std::mutex> lock(g_handleMapMutex);
        auto it = g_handleMap.find(handle);
        if (it != g_handleMap.end()) {
            connWrapper = it->second;
        }
    }

    if (connWrapper) {
        // 格式化可变参数
        va_list args;
        va_start(args, format);

        char buffer[1024];
        // 使用vsnprintf来安全地处理可变参数
        vsnprintf(buffer, sizeof(buffer), format, args);
        
        va_end(args);

        // 调用包装器的log方法
        // 注意：这里我们直接传递格式化后的缓冲区，而不是再次传递可变参数
        connWrapper->log(level, "%s", buffer);
    }
    // 如果在map中找不到句柄，则静默失败，不记录日志。
    // 这是因为此时可能句柄正在创建或销毁，日志系统不应干扰其生命周期。
}

//=============================================================================
// 错误处理辅助函数实现
//=============================================================================

/**
 * @brief 判断错误码类型的辅助函数
 */
int GetErrorCodeType(int errorCode) {
    if (errorCode == 0 || errorCode == 1) {
        return 0; // 成功
    }

    // 通讯错误 (TIMEOUT=100, FORMATERROR=101, CONNECTIONERROR=102)
    if (errorCode == 100 || errorCode == 101 || errorCode == 102) {
        return 1; // 通讯错误
    }

    // DSP错误 (负数)
    if (errorCode < 0) {
        return 2; // DSP错误
    }

    // 伺服错误 (十六进制范围，通常 > 0x0100)
    if (errorCode >= 0x0101) {
        return 4; // 伺服错误
    }

    // DLL内部错误 (其他正数)
    return 3; // DLL内部错误
}

/**
 * @brief 将宽字符串转换为UTF-8字符串 (原生C++实现)
 */
std::string WStringToUTF8(const wchar_t* wstr) {
    if (!wstr) return "";

    std::wstring ws(wstr);
    if (ws.empty()) return "";

    std::string result;
    result.reserve(ws.length() * 3); // UTF-8最多3字节per字符(BMP范围)

    for (wchar_t wc : ws) {
        if (wc <= 0x7F) {
            // ASCII字符 (0-127)
            result.push_back(static_cast<char>(wc));
        } else if (wc <= 0x7FF) {
            // 2字节UTF-8 (128-2047)
            result.push_back(static_cast<char>(0xC0 | (wc >> 6)));
            result.push_back(static_cast<char>(0x80 | (wc & 0x3F)));
        } else {
            // 3字节UTF-8 (2048-65535, BMP范围)
            result.push_back(static_cast<char>(0xE0 | (wc >> 12)));
            result.push_back(static_cast<char>(0x80 | ((wc >> 6) & 0x3F)));
            result.push_back(static_cast<char>(0x80 | (wc & 0x3F)));
        }
    }

    return result;
}

/**
 * @brief 统一的错误处理函数 - 记录错误日志并返回错误码
 */
short LogAndReturnError(TADMotionConn* handle, int errorCode, const char* functionName, const char* format, ...) {
    if (!handle || !functionName) {
        return errorCode;
    }

    // 获取错误码对应的描述信息
    const wchar_t* errorDesc = GetMotionErrorString(errorCode);
    std::string errorDescStr = WStringToUTF8(errorDesc);

    // 判断错误类型，决定显示格式
    int errorType = GetErrorCodeType(errorCode);
    char baseErrorMsg[512];

    if (errorType == 4) {
        // 伺服错误：使用十六进制格式，转换为无符号类型避免负数显示
        unsigned short unsignedErrorCode = static_cast<unsigned short>(errorCode);
        snprintf(baseErrorMsg, sizeof(baseErrorMsg), "%s: 错误码=0x%04X, 描述=%s",
                 functionName, unsignedErrorCode, errorDescStr.c_str());
    } else {
        // DLL内部错误、通讯错误、DSP错误：使用十进制格式
        snprintf(baseErrorMsg, sizeof(baseErrorMsg), "%s: 错误码=%d, 描述=%s",
                 functionName, errorCode, errorDescStr.c_str());
    }

    // 如果有额外的格式化信息，则添加
    if (format) {
        va_list args;
        va_start(args, format);

        char additionalMsg[256];
        vsnprintf(additionalMsg, sizeof(additionalMsg), format, args);
        va_end(args);

        // 组合完整的错误信息
        char fullErrorMsg[768];
        snprintf(fullErrorMsg, sizeof(fullErrorMsg), "%s, 详情: %s", baseErrorMsg, additionalMsg);
        g_Log(handle, LOG_ERROR, "%s", fullErrorMsg);
    } else {
        g_Log(handle, LOG_ERROR, "%s", baseErrorMsg);
    }

    return errorCode;
}

/**
 * @brief SendCommand错误处理函数 - 区分通讯错误和DSP错误
 */
short LogSendCommandError(TADMotionConn* handle, int errorCode, const char* functionName, const char* commandInfo) {
    if (!handle || !functionName) {
        return errorCode;
    }

    int errorType = GetErrorCodeType(errorCode);
    const wchar_t* errorDesc = GetMotionErrorString(errorCode);
    std::string errorDescStr = WStringToUTF8(errorDesc);

    const char* errorTypeStr = "";
    LogLevel logLevel = LOG_ERROR;

    switch (errorType) {
        case 1: // 通讯错误
            errorTypeStr = "通讯错误";
            logLevel = LOG_ERROR;
            break;
        case 2: // DSP错误
            errorTypeStr = "DSP底层错误";
            logLevel = LOG_WARNING;
            break;
        case 4: // 伺服错误
            errorTypeStr = "伺服驱动器错误";
            logLevel = LOG_ERROR;
            break;
        default:
            errorTypeStr = "未知错误";
            logLevel = LOG_ERROR;
            break;
    }

    // 构建错误信息，根据错误类型决定显示格式
    if (commandInfo) {
        if (errorType == 4) {
            // 伺服错误：使用十六进制格式，转换为无符号类型避免负数显示
            unsigned short unsignedErrorCode = static_cast<unsigned short>(errorCode);
            g_Log(handle, logLevel, "%s: SendCommand失败 - %s, 错误码=0x%04X, 描述=%s, 命令信息: %s",
                  functionName, errorTypeStr, unsignedErrorCode, errorDescStr.c_str(), commandInfo);
        } else {
            // 通讯错误、DSP错误：使用十进制格式
            g_Log(handle, logLevel, "%s: SendCommand失败 - %s, 错误码=%d, 描述=%s, 命令信息: %s",
                  functionName, errorTypeStr, errorCode, errorDescStr.c_str(), commandInfo);
        }
    } else {
        if (errorType == 4) {
            // 伺服错误：使用十六进制格式，转换为无符号类型避免负数显示
            unsigned short unsignedErrorCode = static_cast<unsigned short>(errorCode);
            g_Log(handle, logLevel, "%s: SendCommand失败 - %s, 错误码=0x%04X, 描述=%s",
                  functionName, errorTypeStr, unsignedErrorCode, errorDescStr.c_str());
        } else {
            // 通讯错误、DSP错误：使用十进制格式
            g_Log(handle, logLevel, "%s: SendCommand失败 - %s, 错误码=%d, 描述=%s",
                  functionName, errorTypeStr, errorCode, errorDescStr.c_str());
        }
    }

    return errorCode;
}
