/**
 * @file io_control_test.cpp
 * @brief IO控制测试程序
 *
 * 本程序用于测试ADMotion库中的IO控制相关函数
 */

#include <iostream>
#include <string>
#include <chrono>
#include <thread>
#include <vector>
#include "motion_api.h"
#include "motion_types.h"

// 打印错误信息
void printError(const std::string& operation, MotionErrorCode errorCode) {
    std::cout << "操作 '" << operation << "' 失败: "
              << getMotionErrorDetails(errorCode) << std::endl;
}

// 测试设备输入输出
void testDeviceIO(MotionAPI& api) {
    std::cout << "\n=== 测试设备输入输出 ===" << std::endl;

    // 获取设备输入
    auto input = api.getDeviceInput();
    if (input.hasValue()) {
        std::cout << "设备输入: 0x" << std::hex << input.value() << std::dec << std::endl;
    } else {
        std::cout << "获取设备输入失败" << std::endl;
    }

    // 获取设备输出
    auto output = api.getDeviceOutput();
    if (output.hasValue()) {
        std::cout << "设备输出: 0x" << std::hex << output.value() << std::dec << std::endl;
    } else {
        std::cout << "获取设备输出失败" << std::endl;
    }

    // 设置设备输出
    int32_t newOutput = 0x00000001; // 设置第一位为1
    auto result = api.setDeviceOutput(newOutput);
    if (result == MotionErrorCode::Success) {
        std::cout << "设置设备输出成功: 0x" << std::hex << newOutput << std::dec << std::endl;

        // 再次获取设备输出，验证设置是否成功
        output = api.getDeviceOutput();
        if (output.hasValue()) {
            std::cout << "设备输出: 0x" << std::hex << output.value() << std::dec << std::endl;
        }
    } else {
        printError("设置设备输出", result);
    }
}

// 测试高速IO参数
void testSpeedIOParam(MotionAPI& api) {
    std::cout << "\n=== 测试高速IO参数 ===" << std::endl;

    // 设置高速IO参数
    short ioNum = 1;
    short duty = 500;    // 占空比500us
    short period = 1000; // 周期1000us

    auto result = api.setSpeedIOParam(ioNum, duty, period);
    if (result == MotionErrorCode::Success) {
        std::cout << "设置高速IO参数成功" << std::endl;
    } else {
        printError("设置高速IO参数", result);
    }

    // 手动控制高速IO电平
    short switchState = 1; // 开
    result = api.setSpeedIOState(ioNum, switchState);
    if (result == MotionErrorCode::Success) {
        std::cout << "设置高速IO电平成功: " << switchState << std::endl;

        // 等待一段时间
        std::this_thread::sleep_for(std::chrono::milliseconds(500));

        // 关闭高速IO
        switchState = 0; // 关
        result = api.setSpeedIOState(ioNum, switchState);
        if (result == MotionErrorCode::Success) {
            std::cout << "关闭高速IO成功" << std::endl;
        } else {
            printError("关闭高速IO", result);
        }
    } else {
        printError("设置高速IO电平", result);
    }
}

// 测试IO脉冲功能
void testIOPulse(MotionAPI& api) {
    std::cout << "\n=== 测试IO脉冲功能 ===" << std::endl;

    short crd = 0;
    short ioNum = 1;

    // 设置IO脉冲使能
    auto result = api.setIOPulseEnable(crd, ioNum, 1);
    if (result == MotionErrorCode::Success) {
        std::cout << "设置IO脉冲使能成功" << std::endl;

        // 设置IO脉冲状态
        result = api.setIOPulseState(crd, ioNum, 1);
        if (result == MotionErrorCode::Success) {
            std::cout << "设置IO脉冲状态成功" << std::endl;

            // 设置IO脉冲触发
            result = api.setIOPulseTrigger(crd, ioNum, 1);
            if (result == MotionErrorCode::Success) {
                std::cout << "设置IO脉冲触发成功" << std::endl;

                // 等待一段时间
                std::this_thread::sleep_for(std::chrono::milliseconds(500));

                // 关闭IO脉冲触发
                result = api.setIOPulseTrigger(crd, ioNum, 0);
                if (result == MotionErrorCode::Success) {
                    std::cout << "关闭IO脉冲触发成功" << std::endl;
                } else {
                    printError("关闭IO脉冲触发", result);
                }
            } else {
                printError("设置IO脉冲触发", result);
            }
        } else {
            printError("设置IO脉冲状态", result);
        }

        // 关闭IO脉冲使能
        result = api.setIOPulseEnable(crd, ioNum, 0);
        if (result == MotionErrorCode::Success) {
            std::cout << "关闭IO脉冲使能成功" << std::endl;
        } else {
            printError("关闭IO脉冲使能", result);
        }
    } else {
        printError("设置IO脉冲使能", result);
    }
}

// 测试速度IO点位
void testSpeedIOPoint(MotionAPI& api) {
    std::cout << "\n=== 测试速度IO点位 ===" << std::endl;

    short crd = 0;

    // 创建点位数组
    std::vector<SpeedIOPoint> points;

    // 添加几个点位
    SpeedIOPoint p1;
    p1.x = 100.0;
    p1.y = 100.0;
    p1.isOpen = 1;
    p1.openStyle = 0;
    points.push_back(p1);

    SpeedIOPoint p2;
    p2.x = 200.0;
    p2.y = 200.0;
    p2.isOpen = 0;
    p2.openStyle = 0;
    points.push_back(p2);

    // 发送速度IO点位
    auto result = api.sendSpeedIOPoint(crd, points);
    if (result == MotionErrorCode::Success) {
        std::cout << "发送速度IO点位成功" << std::endl;

        // 启用速度IO
        short ioNum = 1;
        result = api.speedIOEnable(crd, 1, ioNum);
        if (result == MotionErrorCode::Success) {
            std::cout << "启用速度IO成功" << std::endl;

            // 等待一段时间
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));

            // 禁用速度IO
            result = api.speedIOEnable(crd, 0, ioNum);
            if (result == MotionErrorCode::Success) {
                std::cout << "禁用速度IO成功" << std::endl;
            } else {
                printError("禁用速度IO", result);
            }
        } else {
            printError("启用速度IO", result);
        }

        // 清除速度IO点位
        result = api.speedIOClearPoint(crd);
        if (result == MotionErrorCode::Success) {
            std::cout << "清除速度IO点位成功" << std::endl;
        } else {
            printError("清除速度IO点位", result);
        }
    } else {
        printError("发送速度IO点位", result);
    }
}

// 测试获取FPGA版本
void testGetFpgaVersion(MotionAPI& api) {
    std::cout << "\n=== 测试获取FPGA版本 ===" << std::endl;

    auto version = api.getFpgaVersion();
    if (version.hasValue()) {
        std::cout << "FPGA版本: " << version.value() << std::endl;
    } else {
        std::cout << "获取FPGA版本失败" << std::endl;
    }
}

int main() {
    std::cout << "=== ADMotion IO控制测试程序 ===" << std::endl;

    // 创建MotionAPI对象
    MotionAPI api;

    // 连接到板卡
    std::string ip = "************"; // 请根据实际情况修改IP地址
    int port = 8000;

    std::cout << "正在连接到板卡: " << ip << ":" << port << std::endl;
    auto result = api.openBoard(ip, port);

    if (result == MotionErrorCode::Success) {
        std::cout << "连接成功!" << std::endl;

        // 测试各种IO功能
        testDeviceIO(api);
        testSpeedIOParam(api);
        testIOPulse(api);
        testSpeedIOPoint(api);
        testGetFpgaVersion(api);

        // 关闭板卡连接
        result = api.closeBoard();
        if (result == MotionErrorCode::Success) {
            std::cout << "\n关闭板卡连接成功" << std::endl;
        } else {
            printError("关闭板卡连接", result);
        }
    } else {
        printError("连接板卡", result);
    }

    std::cout << "\n测试完成，按任意键退出..." << std::endl;
    std::cin.get();

    return 0;
}
