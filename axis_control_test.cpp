#include "motion_api.h"
#include <iostream>
#include <iomanip>
#include <array>
#include <chrono>
#include <thread>

// 测试轴控制功能
void testAxisControl(MotionAPI& api) {
    std::cout << "=== 测试轴控制功能 ===" << std::endl;

    // 设置轴参数
    std::cout << "设置轴参数..." << std::endl;

    std::array<short, MAX_CRD_AXIS> axisMap = {0, 1};
    std::array<short, MAX_CRD_AXIS> axisDir = {1, 1};
    std::array<int32_t, MAX_CRD_AXIS> velMax = {10000, 10000};
    std::array<int32_t, MAX_CRD_AXIS> accMax = {100000, 100000};
    std::array<int32_t, MAX_CRD_AXIS> positive = {1000000, 1000000};
    std::array<int32_t, MAX_CRD_AXIS> negative = {-1000000, -1000000};

    MotionErrorCode result = api.setAxisPrm(0, axisMap, axisDir, velMax, accMax, positive, negative);

    if (result == MotionErrorCode::Success) {
        std::cout << "设置轴参数成功" << std::endl;
    } else {
        std::cout << "设置轴参数失败: " << getMotionErrorDetails(result) << std::endl;
    }

    // 获取轴状态
    std::cout << "获取轴状态..." << std::endl;

    for (short axis = 0; axis < 4; ++axis) {
        std::cout << "轴 " << axis << ": ";
        auto status = api.getAxisStatus(axis);

        if (status) {
            std::cout << getAxisStatusDescription(*status) << std::endl;
        } else {
            std::cout << "获取状态失败" << std::endl;
        }
    }

    // 使能轴
    std::cout << "使能轴 0..." << std::endl;

    result = api.axisOn(0);

    if (result == MotionErrorCode::Success) {
        std::cout << "使能轴 0 成功" << std::endl;
    } else {
        std::cout << "使能轴 0 失败: " << getMotionErrorDetails(result) << std::endl;
    }

    // 再次获取轴状态
    std::cout << "再次获取轴 0 状态..." << std::endl;

    auto status = api.getAxisStatus(0);

    if (status) {
        std::cout << "轴 0 状态: " << getAxisStatusDescription(*status) << std::endl;
    } else {
        std::cout << "获取轴 0 状态失败" << std::endl;
    }

    // 设置JOG模式
    std::cout << "设置JOG模式..." << std::endl;

    result = api.setJogMode(0);

    if (result == MotionErrorCode::Success) {
        std::cout << "设置JOG模式成功" << std::endl;
    } else {
        std::cout << "设置JOG模式失败: " << getMotionErrorDetails(result) << std::endl;
    }

    // 设置JOG参数
    std::cout << "设置JOG参数..." << std::endl;

    TJogPrm jogPrm;
    jogPrm.acc = 100000;
    jogPrm.dec = 100000;
    jogPrm.Maxvel = 10000;
    jogPrm.rate = 1;

    result = api.setJogPrm(0, jogPrm);

    if (result == MotionErrorCode::Success) {
        std::cout << "设置JOG参数成功" << std::endl;
    } else {
        std::cout << "设置JOG参数失败: " << getMotionErrorDetails(result) << std::endl;
    }

    // JOG运动
    std::cout << "JOG运动正向..." << std::endl;

    result = api.jogUpdate(0, 1);

    if (result == MotionErrorCode::Success) {
        std::cout << "JOG运动正向成功" << std::endl;
    } else {
        std::cout << "JOG运动正向失败: " << getMotionErrorDetails(result) << std::endl;
    }

    // 等待1秒
    std::cout << "等待1秒..." << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(1));

    // 停止JOG运动
    std::cout << "停止JOG运动..." << std::endl;

    result = api.jogUpdate(0, 0);

    if (result == MotionErrorCode::Success) {
        std::cout << "停止JOG运动成功" << std::endl;
    } else {
        std::cout << "停止JOG运动失败: " << getMotionErrorDetails(result) << std::endl;
    }

    // 获取轴位置
    std::cout << "获取轴位置..." << std::endl;

    auto pos = api.getAxisPos(0);

    if (pos) {
        std::cout << "轴 0 位置: " << std::fixed << std::setprecision(3) << *pos << std::endl;
    } else {
        std::cout << "获取轴 0 位置失败" << std::endl;
    }

    // 关闭轴使能
    std::cout << "关闭轴 0 使能..." << std::endl;

    result = api.axisOff(0);

    if (result == MotionErrorCode::Success) {
        std::cout << "关闭轴 0 使能成功" << std::endl;
    } else {
        std::cout << "关闭轴 0 使能失败: " << getMotionErrorDetails(result) << std::endl;
    }

    // 再次获取轴状态
    std::cout << "再次获取轴 0 状态..." << std::endl;

    status = api.getAxisStatus(0);

    if (status) {
        std::cout << "轴 0 状态: " << getAxisStatusDescription(*status) << std::endl;
    } else {
        std::cout << "获取轴 0 状态失败" << std::endl;
    }
}

// 主函数
int main() {
    // 创建MotionAPI对象
    MotionAPI api(MotionConn::create());

    // 打开板卡
    std::string ip = "***********";
    int port = 6666;

    std::cout << "连接到 " << ip << ":" << port << std::endl;
    MotionErrorCode result = api.openBoard(ip, port);

    if (result == MotionErrorCode::Success) {
        std::cout << "连接成功" << std::endl;
    } else {
        std::cout << "连接失败: " << getMotionErrorDetails(result) << std::endl;
        return 1;
    }

    // 测试轴控制功能
    testAxisControl(api);

    // 关闭板卡
    std::cout << "=== 测试完成，关闭板卡 ===" << std::endl;
    result = api.closeBoard();

    if (result == MotionErrorCode::Success) {
        std::cout << "关闭成功" << std::endl;
    } else {
        std::cout << "关闭失败: " << getMotionErrorDetails(result) << std::endl;
    }

    return 0;
}
