﻿#include "math.h"
#include "crdmath.h"
#include "card.h"
#include "ADOSType.h"
#include <fstream>

void GetArcCenter(double* pStartPoint, double* pEndPoint, double* pArcCenter, short circleDir, double radius)
{
    //圆心坐标和顺时针、逆时针、大于180度或者小于180度均有关系（顺时针，小于180度圆弧）
    double midPoint[2];
    double length; //圆心至起点终点直线段的距离
    double angle;
    double temp1, temp2, temp3;

    midPoint[0] = (pStartPoint[0] + pEndPoint[0]) / 2;
    midPoint[1] = (pStartPoint[1] + pEndPoint[1]) / 2;

    temp1  = radius;
    temp2  = midPoint[0] - pStartPoint[0];
    temp3  = midPoint[1] - pStartPoint[1];
    length = sqrt(temp1 * temp1 - temp2 * temp2 - temp3 * temp3);
    //求解起点至终点向量与x轴夹角
    angle = atan2((pEndPoint[1] - pStartPoint[1]), (pEndPoint[0] - pStartPoint[0]));
    if(((circleDir == 0) && (radius > 0)) || ((circleDir == 1) && (radius < 0)))
        { //顺时针并且小于180度  或者 逆时针并且大于180度弧
            pArcCenter[0] = midPoint[0] + length * sin(angle);
            pArcCenter[1] = midPoint[1] - length * cos(angle);
        }
    else if(((circleDir == 0) && (radius < 0)) || ((circleDir == 1) && (radius > 0)))
        { //顺时针并且大于180度 或者 逆时针并且小于180度弧
            pArcCenter[0] = midPoint[0] - length * sin(angle);
            pArcCenter[1] = midPoint[1] + length * cos(angle);
        }
}

//计算两个向量的叉积
void GetVectorCross(double* u, double* v, double* a)
{ //叉积公式：u x v = { u2v3-v2u3 , u3v1-v3u1 , u1v2-u2v1 }
    a[0] = u[1] * v[2] - v[1] * u[2];
    a[1] = u[2] * v[0] - v[2] * u[0];
    a[2] = u[0] * v[1] - v[0] * u[1];
}

//求向量的模
double GetVectorNorm(double* data)
{
    return (sqrt(data[0] * data[0] + data[1] * data[1] + data[2] * data[2]));
}

//向量点乘
double GetVectorDot(double* data1, double* data2)
{
    return (data1[0] * data2[0] + data1[1] * data2[1] + data1[2] * data2[2]);
}

void GetCirclePoint(double* pointB, double* tanVector, double* P)
{
    for(int i = 0; i < 3; i++)
        {
            P[i] = pointB[i] - 2 * tanVector[i];
        }
}

//求最小值函数
double MinSpeed(double a, double b, double c)
{
    double min;
    if(a <= b)
        {
            min = a;
        }
    else
        {
            min = b;
        }
    if(min > c)
        {
            min = c;
        }
    return min;
}

double abs_z(double a)
{
    double b = 0;
    if(a > 0)
        {
            b = a;
        }
    else if(a < 0)
        {
            b = -a;
        }
    else if(a == 0)
        {
            b = 0;
        }
    return b;
}

void VectorCross(double* Vector01, double* Vector02, double* calVector)
{
    calVector[0] = Vector01[1] * Vector02[2] - Vector02[1] * Vector01[2];
    calVector[1] = Vector01[2] * Vector02[0] - Vector01[0] * Vector02[2];
    calVector[2] = Vector01[0] * Vector02[1] - Vector01[1] * Vector02[0];
}

short CalArcRadius_dir(int32_t* p1, int32_t* p2, int32_t* p3, double* radius1, double* circleDir1)
{
    double Vector01[3], Vector02[3], calVector01[3];
    for(int i = 0; i < 3; i++)
    {
        Vector01[i] = p2[i] - p1[i];
        Vector02[i] = p3[i] - p2[i];
    }
    VectorCross(Vector01, Vector02, calVector01);
    if(calVector01[2] < 0)
    {
        *circleDir1 = 0;
    }
    else if(calVector01[2] > 0)
    {
        *circleDir1 = 1;
    }

    long long a, b, c, d, e, f;
    double x0(0), y0(0);
    a = p1[0] - p2[0];
    b = p1[1] - p2[1];
    c = p1[0] - p3[0];
    d = p1[1] - p3[1];

    e = (((long long)p1[0] * (long long)p1[0] - (long long)p2[0] * (long long)p2[0]) -
         ((long long)p2[1] * (long long)p2[1] - (long long)p1[1] * (long long)p1[1])) /2 ; //* 0.5;
    f = (((long long)p1[0] * (long long)p1[0] - (long long)p3[0] * (long long)p3[0]) -
         ((long long)p3[1] * (long long)p3[1] - (long long)p1[1] * (long long)p1[1])) /2 ; //* 0.5;

    if(0 == (c * b - a * d))
    {
        return CMD_API_ERROR_CRD_ARC3D_COLLINEAR;
    }
    else
    {
        x0 = ((double)(b * f - d * e)) / ((double)(c * b - a * d));
        y0 = ((double)(c * e - a * f)) / ((double)(b * c - a * d));
    }
    double R;
    double R1 = (p1[0] - x0) * (p1[0] - x0);
    double R2 = (p1[1] - y0) * (p1[1] - y0);
    R=sqrt(R1+R2);
    *radius1 = R;
    return CMD_SUCCESS;
}

short CalArcCenter_dir(int32_t* p1, int32_t* p2, int32_t* p3, double* center, short* circleDir1)
{
        double Vector01[3], Vector02[3], calVector01[3];
    for(int i = 0; i < 3; i++)
        {
            Vector01[i] = p2[i] - p1[i];
            Vector02[i] = p3[i] - p2[i];
        }
    VectorCross(Vector01, Vector02, calVector01);
    if(calVector01[2] < 0)
        {
            *circleDir1 = 0;
        }
    else if(calVector01[2] > 0)
        {
            *circleDir1 = 1;
        }

    long long a, b, c, d, e, f ;
    double  x0(0), y0(0);
    a = p1[0] - p2[0];
    b = p1[1] - p2[1];
    c = p1[0] - p3[0];
    d = p1[1] - p3[1];

    e = (((long long)p1[0] * (long long)p1[0] - (long long)p2[0] * (long long)p2[0]) -
         ((long long)p2[1] * (long long)p2[1] - (long long)p1[1] * (long long)p1[1])) /2 ; //* 0.5;
    f = (((long long)p1[0] * (long long)p1[0] - (long long)p3[0] * (long long)p3[0]) -
         ((long long)p3[1] * (long long)p3[1] - (long long)p1[1] * (long long)p1[1])) /2 ; //* 0.5;

    if((0 == (c * b - a * d)))
    {
        return CMD_API_ERROR_CRD_ARC3D_COLLINEAR;
    }
    else
    {
        x0 = (double)(b * f - d * e) / (double)(c * b - a * d);
        y0 = (double)(c * e - a * f) / (double)(b * c - a * d);
    }
    center[0] = x0;
    center[1] = y0;
    return CMD_SUCCESS;
}

short readData(long data1[50][2],short *length1)
{
    using namespace std;
    long data[50][2];//定义一个1500*2的矩阵，用于存放数据

    ifstream infile;//定义读取文件流，相对于程序来说是in

    infile.open("E:/lookahead/ADHCode/Source/ADMotiondata.csv");//打开文件
    if (!infile) {//文件没打开
        return 1;
    }
    for (int i = 0; i < 100; i++)//定义行循环

    {

        for (int j = 0; j < 2; j++)//定义列循环

        {

            infile >> data[i][j];//读取一个值(空格、制表符、换行隔开)就写入到矩阵中，行列不断循环进行
        }
        if (data[i][0] == 0)
        {
            *length1 = i;
            break;
        }

    }
    memcpy(data1, data, sizeof(data));
    infile.close();//读取完成之后关闭文件
    return 0;
}
