#include "motion_api.h"
#include <iostream>
#include <iomanip>

// 打印轴状态
void printAxisStatus(const Optional<AxisStatus>& status) {
    if (!status.hasValue()) {
        std::cout << "获取轴状态失败" << std::endl;
        return;
    }

    std::cout << "轴状态: ";
    if (static_cast<uint16_t>(status.value()) == 0) {
        std::cout << "无状态" << std::endl;
        return;
    }

    if (static_cast<uint16_t>(status.value() & AxisStatus::Enable)) {
        std::cout << "使能 ";
    }
    if (static_cast<uint16_t>(status.value() & AxisStatus::PositiveLimit)) {
        std::cout << "正限位 ";
    }
    if (static_cast<uint16_t>(status.value() & AxisStatus::NegativeLimit)) {
        std::cout << "负限位 ";
    }
    if (static_cast<uint16_t>(status.value() & AxisStatus::Origin)) {
        std::cout << "原点 ";
    }
    if (static_cast<uint16_t>(status.value() & AxisStatus::InPosition)) {
        std::cout << "到位 ";
    }
    if (static_cast<uint16_t>(status.value() & AxisStatus::Moving)) {
        std::cout << "运动中 ";
    }
    if (static_cast<uint16_t>(status.value() & AxisStatus::Alarm)) {
        std::cout << "报警 ";
    }
    if (static_cast<uint16_t>(status.value() & AxisStatus::Emergency)) {
        std::cout << "急停 ";
    }
    if (static_cast<uint16_t>(status.value() & AxisStatus::Reset)) {
        std::cout << "复位中 ";
    }
    if (static_cast<uint16_t>(status.value() & AxisStatus::Angle)) {
        std::cout << "角度识别 ";
    }
    std::cout << std::endl;
}

// 测试基本连接功能
void testConnection(MotionAPI& api) {
    std::cout << "=== 测试连接功能 ===" << std::endl;

    // 打开板卡
    std::string ip = "***********";
    int port = 6666;

    std::cout << "连接到 " << ip << ":" << port << std::endl;
    MotionErrorCode result = api.openBoard(ip, port);

    if (result == MotionErrorCode::Success) {
        std::cout << "连接成功" << std::endl;
    } else {
        std::cout << "连接失败: " << getMotionErrorMessage(result) << std::endl;
        return;
    }

    // 重置板卡
    std::cout << "重置板卡..." << std::endl;
    result = api.resetBoard();

    if (result == MotionErrorCode::Success) {
        std::cout << "重置成功" << std::endl;
    } else {
        std::cout << "重置失败: " << getMotionErrorMessage(result) << std::endl;
    }
}

// 测试坐标系位置获取功能
void testGetCrdPos(MotionAPI& api) {
    std::cout << "=== 测试坐标系位置获取功能 ===" << std::endl;

    for (short crd = 0; crd < 4; ++crd) {
        std::cout << "坐标系 " << crd << ": ";
        auto pos = api.getCrdPos(crd);

        if (pos.hasValue()) {
            std::cout << "X=" << std::fixed << std::setprecision(3) << pos.value()[0]
                      << ", Y=" << std::fixed << std::setprecision(3) << pos.value()[1] << std::endl;
        } else {
            std::cout << "获取位置失败" << std::endl;
        }
    }
}

// 测试轴位置获取功能
void testGetAxisPos(MotionAPI& api) {
    std::cout << "=== 测试轴位置获取功能 ===" << std::endl;

    for (short axis = 0; axis < 8; ++axis) {
        std::cout << "轴 " << axis << ": ";
        auto pos = api.getAxisPos(axis);

        if (pos.hasValue()) {
            std::cout << std::fixed << std::setprecision(3) << pos.value() << std::endl;
        } else {
            std::cout << "获取位置失败" << std::endl;
        }
    }
}

// 测试轴状态获取功能
void testGetAxisStatus(MotionAPI& api) {
    std::cout << "=== 测试轴状态获取功能 ===" << std::endl;

    for (short axis = 0; axis < 8; ++axis) {
        std::cout << "轴 " << axis << ": ";
        auto status = api.getAxisStatus(axis);
        printAxisStatus(status);
    }
}

// 测试设备IO功能
void testDeviceIO(MotionAPI& api) {
    std::cout << "=== 测试设备IO功能 ===" << std::endl;

    // 获取设备输入
    auto input = api.getDeviceInput();
    if (input.hasValue()) {
        std::cout << "设备输入: 0x" << std::hex << input.value() << std::dec << std::endl;
    } else {
        std::cout << "获取设备输入失败" << std::endl;
    }

    // 获取设备输出
    auto output = api.getDeviceOutput();
    if (output.hasValue()) {
        std::cout << "设备输出: 0x" << std::hex << output.value() << std::dec << std::endl;
    } else {
        std::cout << "获取设备输出失败" << std::endl;
    }

    // 设置设备输出
    if (output.hasValue()) {
        int32_t newOutput = output.value() ^ 0x01; // 切换最低位
        std::cout << "设置设备输出: 0x" << std::hex << newOutput << std::dec << std::endl;

        MotionErrorCode result = api.setDeviceOutput(newOutput);
        if (result == MotionErrorCode::Success) {
            std::cout << "设置设备输出成功" << std::endl;
        } else {
            std::cout << "设置设备输出失败: " << getMotionErrorMessage(result) << std::endl;
        }

        // 再次获取设备输出
        output = api.getDeviceOutput();
        if (output.hasValue()) {
            std::cout << "设备输出(更新后): 0x" << std::hex << output.value() << std::dec << std::endl;
        }
    }
}


// 主函数
int main() {
    // 创建MotionAPI对象
    MotionAPI api(MotionConn::create());

    // 测试连接功能
    testConnection(api);

    // 测试坐标系位置获取功能
    testGetCrdPos(api);

    // 测试轴位置获取功能
    testGetAxisPos(api);

    // 测试轴状态获取功能
    testGetAxisStatus(api);

    // 测试设备IO功能
    testDeviceIO(api);


    // 关闭板卡
    std::cout << "=== 测试完成，关闭板卡 ===" << std::endl;
    MotionErrorCode result = api.closeBoard();

    if (result == MotionErrorCode::Success) {
        std::cout << "关闭成功" << std::endl;
    } else {
        std::cout << "关闭失败: " << getMotionErrorMessage(result) << std::endl;
    }

    return 0;
}
