#pragma once
#ifndef MOTION_CONN_H
#define MOTION_CONN_H

#include <memory>
#include <string>
#include <stdexcept>
#include "admc_pci.h"

/**
 * @brief 运动连接异常类
 */
class MotionConnException : public std::runtime_error {
public:
    explicit MotionConnException(const std::string& message) : std::runtime_error(message) {}
};

/**
 * @brief TADMotionConn智能指针包装类
 * 使用std::shared_ptr管理TADMotionConn对象的生命周期
 */
class MotionConn {
public:
    /**
     * @brief 创建一个新的TADMotionConn对象
     * @return 包含TADMotionConn对象的MotionConn实例
     * @throws MotionConnException 如果创建失败
     */
    static MotionConn create() {
        TADMotionConn* conn = API_CreateBoard();
        if (!conn) {
            throw MotionConnException("Failed to create motion board");
        }
        return MotionConn(conn);
    }

    /**
     * @brief 默认构造函数
     */
    MotionConn() : m_conn(nullptr, [this](TADMotionConn* c) { this->safeDeleteBoard(c); }) {}

    /**
     * @brief 从TADMotionConn指针构造
     * @param conn TADMotionConn指针
     * @throws std::invalid_argument 如果conn为nullptr
     */
    explicit MotionConn(TADMotionConn* conn) : m_conn(conn, [this](TADMotionConn* c) { this->safeDeleteBoard(c); }) {
        if (!conn) {
            // No logger here yet, can't log.
        }
    }

    /**
     * @brief 获取原始TADMotionConn指针
     * @return TADMotionConn指针
     */
    TADMotionConn* get() const {
        return m_conn.get();
    }

    /**
     * @brief 设置日志回调
     * @param callback 回调函数
     * @param userData 用户数据
     */
    void setLogCallback(LogCallback callback, void* userData) {
        m_logCallback = callback;
        m_logUserData = userData;
    }

    /**
     * @brief 设置日志级别
     * @param level 日志级别
     */
    void setLogLevel(LogLevel level) {
        m_minLogLevel = level;
    }

    /**
     * @brief 内部日志记录函数
     * @param level 日志级别
     * @param message 日志消息
     */
    template<typename... Args>
    void log(LogLevel level, const char* format, Args... args) const {
        if (!m_logCallback || level < m_minLogLevel) {
            return;
        }

        char buffer[1024];
        // Note: consider a more robust formatting library for production
        // 在C++11中，snprintf是线程安全的
        snprintf(buffer, sizeof(buffer), format, args...);
        
        m_logCallback(level, buffer, m_logUserData);
    }

    /**
     * @brief 检查是否有效
     * @return 是否有效
     */
    bool isValid() const {
        return m_conn != nullptr;
    }

    /**
     * @brief 箭头操作符重载
     * @return TADMotionConn指针
     * @throws std::runtime_error 如果m_conn为nullptr
     */
    TADMotionConn* operator->() const {
        if (!isValid()) {
            log(LOG_FATAL, "Attempting to dereference null motion connection");
            throw std::runtime_error("Null motion connection");
        }
        return m_conn.get();
    }

    /**
     * @brief 解引用操作符重载
     * @return TADMotionConn引用
     * @throws std::runtime_error 如果m_conn为nullptr
     */
    TADMotionConn& operator*() const {
        if (!isValid()) {
            log(LOG_FATAL, "Attempting to dereference null motion connection");
            throw std::runtime_error("Null motion connection");
        }
        return *m_conn;
    }

    /**
     * @brief 打开板卡
     * @param ip IP地址
     * @param port 端口号
     * @return 错误码
     * @throws std::invalid_argument 如果ip为nullptr
     */
    short openBoard(const char* ip, int port) {
        if (!ip) {
            log(LOG_ERROR, "Invalid IP address (null)");
            throw std::invalid_argument("Invalid IP address (null)");
        }

        if (!isValid()) {
            log(LOG_ERROR, "Cannot open board: invalid connection");
            return -1;
        }

        log(LOG_INFO, "Opening board at %s:%d", ip, port);
        return API_OpenBoard(m_conn.get(), ip, port);
    }

    /**
     * @brief 打开板卡（std::string版本）
     * @param ip IP地址
     * @param port 端口号
     * @return 错误码
     */
    short openBoard(const std::string& ip, int port) {
        return openBoard(ip.c_str(), port);
    }

    /**
     * @brief 关闭板卡
     * @return 错误码
     */
    short closeBoard() {
        if (!isValid()) {
            log(LOG_ERROR, "Cannot close board: invalid connection");
            return -1;
        }

        log(LOG_INFO, "Closing board connection");
        return API_CloseBoard(m_conn.get());
    }

    /**
     * @brief 重置板卡
     * @return 错误码
     */
    short resetBoard() {
        if (!isValid()) {
            log(LOG_ERROR, "Cannot reset board: invalid connection");
            return -1;
        }

        log(LOG_INFO, "Resetting board");
        return API_ResetBoard(m_conn.get());
    }

private:
    /**
     * @brief 安全删除板卡的自定义删除器
     * @param conn 要删除的TADMotionConn指针
     */
    void safeDeleteBoard(TADMotionConn* conn) {
        if (conn) {
            try {
                log(LOG_INFO, "Deleting motion board handle: %p", conn);
                API_DeleteBoard(conn);
            } catch (...) {
                // 在析构函数中发生异常是危险的，但至少我们能记录下来
                log(LOG_FATAL, "Exception occurred while deleting motion board handle: %p", conn);
            }
        }
    }

    std::shared_ptr<TADMotionConn> m_conn; // TADMotionConn智能指针
    LogCallback m_logCallback = nullptr;
    void* m_logUserData = nullptr;
    LogLevel m_minLogLevel = LOG_INFO;
};

#endif // MOTION_CONN_H
