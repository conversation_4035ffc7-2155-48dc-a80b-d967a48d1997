/**
 * @file admc_export.h
 * @brief ADMotion运动控制库导出接口定义
 * @version 2.0 - 重构版本，使用统一错误码体系
 * @date 2024-12-19
 */

#pragma once
#ifndef ADMC_EXPORT_H
#define ADMC_EXPORT_H

#include "ADOSType.h"
#include "motion_types.h"
#include "motion_enums.h"
#include "motion_error_codes.h"  // 包含统一的错误码定义

// 定义导出宏
#if defined(_WIN32) && !defined(__MINGW64__)
#ifdef MC_API_EXPORT
#define ADMC_EXPORT extern "C" __declspec(dllexport)
#else
#define ADMC_EXPORT extern "C" __declspec(dllimport)
#endif
#else
#define ADMC_EXPORT extern "C" __attribute__((visibility("default")))
#endif

// 注意：错误码定义已移至motion_error_codes.h中
// 这里保留兼容性定义，实际值在motion_error_codes.h中定义

// 基本操作函数
ADMC_EXPORT void* __stdcall ADMC_CreateBoard();
ADMC_EXPORT void __stdcall ADMC_DeleteBoard(void* handle);
ADMC_EXPORT int __stdcall ADMC_OpenBoard(void* handle, const char* ip, int port);
ADMC_EXPORT int __stdcall ADMC_CloseBoard(void* handle);
ADMC_EXPORT int __stdcall ADMC_ResetBoard(void* handle);

// 轴控制函数
ADMC_EXPORT int __stdcall ADMC_SetAxisPrm(void* handle, int crd, int* axisMap, int* axisDir, int* velMax, int* accMax, int* positive, int* negative);
ADMC_EXPORT int __stdcall ADMC_AxisOn(void* handle, int axis);
ADMC_EXPORT int __stdcall ADMC_AxisOff(void* handle, int axis);
ADMC_EXPORT int __stdcall ADMC_GetAxisStatus(void* handle, int axis, int* status);
ADMC_EXPORT int __stdcall ADMC_GetAxisPos(void* handle, int axis, double* pos);

// 点位运动函数
ADMC_EXPORT int __stdcall ADMC_SetCrdTrapMode(void* handle, int crd);
ADMC_EXPORT int __stdcall ADMC_SetCrdTrapPrm(void* handle, int crd, TTrapPrm* prm);
ADMC_EXPORT int __stdcall ADMC_CrdTrapUpdate(void* handle, int crd);
ADMC_EXPORT int __stdcall ADMC_SetAxisTrapMode(void* handle, int crd, int axis);
ADMC_EXPORT int __stdcall ADMC_SetAxisTrapPrm(void* handle, int crd, int axis, TTrapPrm* prm);
ADMC_EXPORT int __stdcall ADMC_AxisTrapUpdate(void* handle, int crd);
ADMC_EXPORT int __stdcall ADMC_JogUpdate(void* handle, int crd, int mask);

// 坐标系插补函数
ADMC_EXPORT int __stdcall ADMC_SetCrdPrm(void* handle, int crd, TCrdPrm* prm);
ADMC_EXPORT int __stdcall ADMC_GetCrdPrm(void* handle, int crd, TCrdPrm* prm);
ADMC_EXPORT int __stdcall ADMC_SendCrdData(void* handle, int crd, TCrdData* data);
ADMC_EXPORT int __stdcall ADMC_CrdStart(void* handle, int crd);
ADMC_EXPORT int __stdcall ADMC_CrdStop(void* handle, int mask, int option, int stopType);
ADMC_EXPORT int __stdcall ADMC_CrdPause(void* handle, int crd);
ADMC_EXPORT int __stdcall ADMC_GetCrdPos(void* handle, int crd, double* pos);

// 直线圆弧插补函数
ADMC_EXPORT int __stdcall ADMC_Ln(void* handle, int crd, int x, int y, double synVel, double synAcc);
ADMC_EXPORT int __stdcall ADMC_ArcXY3Point(void* handle, int crd, int* p1, int* p2, int* p3, double radius, int circleDir, double synVel, double synAcc);
ADMC_EXPORT int __stdcall ADMC_ArcXYR(void* handle, int crd, int x, int y, double radius, int circleDir, double synVel, double synAcc);
ADMC_EXPORT int __stdcall ADMC_ArcXYC(void* handle, int crd, int x, int y, double xCenter, double yCenter, int circleDir, double synVel, double synAcc);

// 前瞻函数
ADMC_EXPORT int __stdcall ADMC_InitLookAhead(void* handle, int crd, double accMax, int count);
ADMC_EXPORT int __stdcall ADMC_CloseLookAhead(void* handle, int crd);

// IO函数
ADMC_EXPORT int __stdcall ADMC_SetDeviceOutput(void* handle, int* deviceOutput);
ADMC_EXPORT int __stdcall ADMC_GetDeviceOutput(void* handle, int* deviceOutput);
ADMC_EXPORT int __stdcall ADMC_GetDeviceInput(void* handle, int* deviceInput);

// 错误处理函数
ADMC_EXPORT const char* __stdcall ADMC_GetErrorMessage(int errorCode);

#endif // ADMC_EXPORT_H
