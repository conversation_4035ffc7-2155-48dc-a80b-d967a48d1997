﻿#pragma once
#include "ADOSType.h"
#include "motion_types.h" // 包含SpeedIOPoint结构体定义
#define DEVICE_MAX 8

#define RES_TYPE_MAX 40

#define COMMAND_DATA_LEN 100

#define COMMAND_PCI_OFFSET 0

#define OPEN_CHANNEL_PCI  0
#define OPEN_CHANNEL_UART 1
#define OPEN_CHANNEL_NET  2

#define PCI_IDLE       0
#define PCI_PC_BUSY    1
#define PCI_PC_FINISH  2
#define PCI_DSP_BUSY   3
#define PCI_DSP_FINISH 4

/*
//用于外部接口交换的数据
TCardIO 一张卡全部的IO，包括轴IO
TCardAxisPos 全部轴的当前坐标
*/
#ifndef DEF_TCardIO
#define DEF_TCardIO
typedef struct
{
    volatile uint16_t positionCrd[4][2];
    volatile uint16_t Input[4];
    volatile uint16_t Output[4];
} TCardIO;
#endif

typedef struct
{
    volatile int32_t X1Pos;  //1轴反馈
    volatile int32_t Y1Pos;  //2轴反馈
    volatile int32_t X2Pos;  //1轴指令
    volatile int32_t Y2Pos;  //2轴指令
    volatile int32_t X3Pos;  //3轴反馈
    volatile int32_t Y3Pos;  //4轴反馈
    volatile int32_t X4Pos;  //3轴指令
    volatile int32_t Y4Pos;  //4轴指令
} TCardAxisPos;

class TADMotionConn;
void Add16ToBuff(TADMotionConn* handle, unsigned short tick, short data);
void Add32ToBuff(TADMotionConn* handle, unsigned short tick, int32_t data);
void Add32Fix16ToBuff(TADMotionConn* handle, unsigned short tick, double data);
void Add32Fix24ToBuff(TADMotionConn* handle, unsigned short tick, double data);
void Add64Fix16ToBuff(TADMotionConn* handle, unsigned short tick, double data);
void AddFloatToBuff(TADMotionConn* handle, unsigned short tick, float data);
void AddFloat64ToBuff(TADMotionConn* handle, unsigned short tick, double data);

unsigned short GetIndex(TADMotionConn* handle);
void           CommandUninitial(TADMotionConn* handle, unsigned short tick);
void           CommandInitial(TADMotionConn* handle, unsigned short tick, unsigned short cmd);

short SendCommand(TADMotionConn* handle, unsigned short tick);
short PostCommand(TADMotionConn* handle);

void Get16FromBuff(TADMotionConn* handle, unsigned short tick, short* pData);
void Get32FromBuff(TADMotionConn* handle, unsigned short tick, int32_t* pData);
void Get32Fix16FromBuff(TADMotionConn* handle, unsigned short tick, double* pData);
void Get32Fix24FromBuff(TADMotionConn* handle, unsigned short tick, double* pData);
void Get64Fix16FromBuff(TADMotionConn* handle, unsigned short tick, double* pData);
void GetFloatFromBuff(TADMotionConn* handle, unsigned short tick, float* pData);
void GetFloat64FromBuff(TADMotionConn* handle, unsigned short tick, double* pData);

short MC_SetDeviceOutput(TADMotionConn* handle, int* deviceOutput);
short MC_GetDeviceOutput(TADMotionConn* handle, int32_t* deviceOutput);
short MC_GetDeviceInput(TADMotionConn* handle, int32_t* deviceInput);
short MC_SetSpeedIOParam(TADMotionConn* handle, short io_num, short duty, short period);
short MC_SetSpeedIOState(TADMotionConn* handle, short io_num, short switch_state);
short MC_SetIOPluseEnable(TADMotionConn* handle, short crd,short io_num, short IO_Enable);
short MC_SetIOPluseState(TADMotionConn* handle, short crd,short io_num, short IO_State);
short MC_SetIOPluseTrigger(TADMotionConn* handle, short crd,short io_num, short IO_Trigger);
short MC_GetFpgaVersion(TADMotionConn* handle,short &version);

// 使用motion_types.h中定义的SpeedIOPoint结构体
short MC_SendSpeedIO_Point(TADMotionConn* handle, short crd, short pointNum, SpeedIOPoint p[]);
short MC_SpeedIO_Enable(TADMotionConn* handle, short crd,short enable,short IO_num);
short MC_SpeedIO_ClearPoint(TADMotionConn* handle, short crd);


short MC_GetAxisStatus(TADMotionConn* handle, short axis, short& Sts);
short MC_ServoParaSet(TADMotionConn* handle, short station_num, short commandServo, short addr, short data);

short MC_DebugModelOption(TADMotionConn* handle, bool status); //自动重连开关
