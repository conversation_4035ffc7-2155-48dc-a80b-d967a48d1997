#include "motion_api.h"
#include "motion_error_codes.h"  // 使用新的错误码头文件
#include <string>

// 将错误码转换为MotionErrorCode枚举
MotionErrorCode convertToMotionErrorCode(short errorCode) {
    return static_cast<MotionErrorCode>(errorCode);
}

// 将MotionErrorCode枚举转换为short
short convertToShort(MotionErrorCode errorCode) {
    return static_cast<short>(errorCode);
}

// 获取错误码对应的错误信息
std::string getMotionErrorMessage(MotionErrorCode code) {
    // 使用新的统一错误信息函数
    const wchar_t* wstr = GetMotionErrorString(static_cast<int>(code));
    // 将宽字符串转换为窄字符串
    std::wstring ws(wstr);
    return std::string(ws.begin(), ws.end());
}

// 获取错误码对应的详细错误信息
std::string getMotionErrorDetails(MotionErrorCode code) {
    std::string message = getMotionErrorMessage(code);
    return "错误码: " + std::to_string(static_cast<int>(code)) + ", 描述: " + message;
}

// 获取轴状态对应的描述
std::string getAxisStatusDescription(AxisStatus status) {
    std::string description;

    if (static_cast<uint16_t>(status) == 0) {
        return "无状态";
    }

    if (static_cast<uint16_t>(status & AxisStatus::Enable)) {
        description += "使能 ";
    }
    if (static_cast<uint16_t>(status & AxisStatus::PositiveLimit)) {
        description += "正限位 ";
    }
    if (static_cast<uint16_t>(status & AxisStatus::NegativeLimit)) {
        description += "负限位 ";
    }
    if (static_cast<uint16_t>(status & AxisStatus::Origin)) {
        description += "原点 ";
    }
    if (static_cast<uint16_t>(status & AxisStatus::InPosition)) {
        description += "到位 ";
    }
    if (static_cast<uint16_t>(status & AxisStatus::Moving)) {
        description += "运动中 ";
    }
    if (static_cast<uint16_t>(status & AxisStatus::Alarm)) {
        description += "报警 ";
    }
    if (static_cast<uint16_t>(status & AxisStatus::Emergency)) {
        description += "急停 ";
    }
    if (static_cast<uint16_t>(status & AxisStatus::Reset)) {
        description += "复位中 ";
    }
    if (static_cast<uint16_t>(status & AxisStatus::Angle)) {
        description += "角度识别 ";
    }

    return description;
}

// 将short状态值转换为AxisStatus枚举
AxisStatus convertToAxisStatus(short status) {
    return static_cast<AxisStatus>(status);
}

// 默认构造函数
MotionAPI::MotionAPI()
    : m_conn(MotionConn::create())
    , m_systemStatus(SystemStatus::Disconnected)
    , m_lastErrorDetails("")
    , m_nextCallbackId(0) {
    m_conn.log(LOG_INFO, "MotionAPI初始化 (自动创建连接)");
}

// 从MotionConn构造
MotionAPI::MotionAPI(const MotionConn& conn)
    : m_conn(conn)
    , m_systemStatus(SystemStatus::Connected)
    , m_lastErrorDetails("")
    , m_nextCallbackId(0) {
    m_conn.log(LOG_INFO, "MotionAPI初始化 (使用现有连接)");
}

// 析构函数
MotionAPI::~MotionAPI() {
    // 关闭板卡连接
    if (m_conn.isValid()) {
        closeBoard();
    }

    // 记录日志
    m_conn.log(LOG_INFO, "MotionAPI销毁");
}

void MotionAPI::setLogCallback(LogCallback callback, void* userData) {
    m_conn.setLogCallback(callback, userData);
    // 可以在这里加一条日志，说明回调已被设置
    m_conn.log(LOG_INFO, "日志回调函数已设置。");
}

// 打开板卡
MotionErrorCode MotionAPI::openBoard(const std::string& ip, int port) {
    if (!m_conn.isValid()) {
        logError(MotionErrorCode::ApiErrorOpen, "打开板卡");
        return MotionErrorCode::ApiErrorOpen;
    }

    m_conn.log(LOG_INFO, "正在连接到板卡: %s:%d", ip.c_str(), port);
    updateSystemStatus(SystemStatus::Connecting);

    short result = m_conn.openBoard(ip.c_str(), port);
    MotionErrorCode errorCode = convertToMotionErrorCode(result);

    if (errorCode == MotionErrorCode::Success) {
        m_conn.log(LOG_INFO, "连接板卡成功");
        updateSystemStatus(SystemStatus::Connected);
    } else {
        logError(errorCode, "打开板卡");
        updateSystemStatus(SystemStatus::Disconnected);
    }

    return errorCode;
}

// 关闭板卡
MotionErrorCode MotionAPI::closeBoard() {
    if (!m_conn.isValid()) {
        logError(MotionErrorCode::ApiErrorOpen, "关闭板卡");
        return MotionErrorCode::ApiErrorOpen;
    }

    m_conn.log(LOG_INFO, "正在关闭板卡连接");
    updateSystemStatus(SystemStatus::Disconnecting);

    short result = m_conn.closeBoard();
    MotionErrorCode errorCode = convertToMotionErrorCode(result);

    if (errorCode == MotionErrorCode::Success) {
        m_conn.log(LOG_INFO, "关闭板卡成功");
        updateSystemStatus(SystemStatus::Disconnected);
    } else {
        logError(errorCode, "关闭板卡");
    }

    return errorCode;
}

// 重置板卡
MotionErrorCode MotionAPI::resetBoard() {
    if (!m_conn.isValid()) {
        logError(MotionErrorCode::ApiErrorOpen, "重置板卡");
        return MotionErrorCode::ApiErrorOpen;
    }

    m_conn.log(LOG_INFO, "正在重置板卡");
    updateSystemStatus(SystemStatus::Initializing);

    short result = m_conn.resetBoard();
    MotionErrorCode errorCode = convertToMotionErrorCode(result);

    if (errorCode == MotionErrorCode::Success) {
        m_conn.log(LOG_INFO, "重置板卡成功");
        updateSystemStatus(SystemStatus::Ready);
    } else {
        logError(errorCode, "重置板卡");
        updateSystemStatus(SystemStatus::Error);
    }

    return errorCode;
}

// 轴控制相关函数实现

// 设置轴参数
MotionErrorCode MotionAPI::setAxisPrm(short crd,
                                     const std::array<short, MAX_CRD_AXIS>& axisMap,
                                     const std::array<short, MAX_CRD_AXIS>& axisDir,
                                     const std::array<int32_t, MAX_CRD_AXIS>& velMax,
                                     const std::array<int32_t, MAX_CRD_AXIS>& accMax,
                                     const std::array<int32_t, MAX_CRD_AXIS>& positive,
                                     const std::array<int32_t, MAX_CRD_AXIS>& negative) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    // 创建副本，因为API函数可能会修改参数
    short axisMapCopy[MAX_CRD_AXIS];
    short axisDirCopy[MAX_CRD_AXIS];
    int32_t velMaxCopy[MAX_CRD_AXIS];
    int32_t accMaxCopy[MAX_CRD_AXIS];
    int32_t positiveCopy[MAX_CRD_AXIS];
    int32_t negativeCopy[MAX_CRD_AXIS];

    for (int i = 0; i < MAX_CRD_AXIS; i++) {
        axisMapCopy[i] = axisMap[i];
        axisDirCopy[i] = axisDir[i];
        velMaxCopy[i] = velMax[i];
        accMaxCopy[i] = accMax[i];
        positiveCopy[i] = positive[i];
        negativeCopy[i] = negative[i];
    }

    short result = API_SetAxisPrm(m_conn.get(), crd, axisMapCopy, axisDirCopy,
                                 velMaxCopy, accMaxCopy, positiveCopy, negativeCopy);
    return convertToMotionErrorCode(result);
}

// 使能轴
MotionErrorCode MotionAPI::axisOn(short axis) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_AxisOn(m_conn.get(), axis);
    return convertToMotionErrorCode(result);
}

// 关闭轴使能
MotionErrorCode MotionAPI::axisOff(short axis) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_AxisOff(m_conn.get(), axis);
    return convertToMotionErrorCode(result);
}

// 设置JOG模式
MotionErrorCode MotionAPI::setJogMode(short crd) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_SetJogMode(m_conn.get(), crd);
    return convertToMotionErrorCode(result);
}

// 设置JOG参数
MotionErrorCode MotionAPI::setJogPrm(short crd, int32_t Maxvel, int32_t acc, int32_t dec, int32_t rate) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_SetJogPrm(m_conn.get(), crd, Maxvel, acc, dec, rate);
    return convertToMotionErrorCode(result);
}

// JOG运动更新
MotionErrorCode MotionAPI::jogUpdate(short axis, short dir) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_JogUpdate(m_conn.get(), axis, dir);
    return convertToMotionErrorCode(result);
}

// 清除轴报警
MotionErrorCode MotionAPI::axisClearAlarm(short axis) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_AxisClearAlarm(m_conn.get(), axis);
    return convertToMotionErrorCode(result);
}

// 获取错误码
Optional<short> MotionAPI::getErrorCode(short axis) const {
    if (!m_conn.isValid()) {
        return Optional<short>::nullopt();
    }

    unsigned short errorCode = 0;
    short result = API_GetErrorCode(m_conn.get(),axis, &errorCode);

    if (result != convertToShort(MotionErrorCode::Success)) {
        return Optional<short>::nullopt();
    }

    return errorCode;
}

// 获取坐标系位置
Optional<std::array<double, 2>> MotionAPI::getCrdPos(short crd) const {
    if (!m_conn.isValid()) {
        return Optional<std::array<double, 2>>::nullopt();
    }

    std::array<double, 2> pos{};
    short result = API_GetCrdPos(m_conn.get(), crd, pos.data());

    if (result != convertToShort(MotionErrorCode::Success)) {
        return Optional<std::array<double, 2>>::nullopt();
    }

    return pos;
}

// 获取轴位置
Optional<double> MotionAPI::getAxisPos(short axis) const {
    if (!m_conn.isValid()) {
        return Optional<double>::nullopt();
    }

    double pos = 0.0;
    short result = API_GetAixsPos(m_conn.get(), axis, pos);

    if (result != convertToShort(MotionErrorCode::Success)) {
        return Optional<double>::nullopt();
    }

    return pos;
}

// 获取轴状态
Optional<AxisStatus> MotionAPI::getAxisStatus(short axis) const {
    if (!m_conn.isValid()) {
        return Optional<AxisStatus>::nullopt();
    }

    short status = 0;
    short result = API_GetAxisStatus(m_conn.get(), axis, status);

    if (result != convertToShort(MotionErrorCode::Success)) {
        return Optional<AxisStatus>::nullopt();
    }

    return convertToAxisStatus(status);
}

// 设置设备输出
MotionErrorCode MotionAPI::setDeviceOutput(int32_t output) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_SetDeviceOutput(m_conn.get(), &output);
    return convertToMotionErrorCode(result);
}


// 获取设备输入
Optional<int32_t> MotionAPI::getDeviceInput() const {
    if (!m_conn.isValid()) {
        return Optional<int32_t>::nullopt();
    }

    int32_t input = 0;
    short result = API_GetDeviceInput(m_conn.get(), &input);

    if (result != convertToShort(MotionErrorCode::Success)) {
        return Optional<int32_t>::nullopt();
    }

    return input;
}

// 设置高速IO参数
MotionErrorCode MotionAPI::setSpeedIOParam(short ioNum, short duty, short period) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_SetSpeedIOParam(m_conn.get(), ioNum, duty, period);
    return convertToMotionErrorCode(result);
}

// 手动控制高速IO电平
MotionErrorCode MotionAPI::setSpeedIOState(short ioNum, short switchState) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_SetSpeedIOState(m_conn.get(), ioNum, switchState);
    return convertToMotionErrorCode(result);
}

// 设置IO脉冲使能
MotionErrorCode MotionAPI::setIOPulseEnable(short crd, short ioNum, short ioEnable) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_SetIOPluseEnable(m_conn.get(), crd, ioNum, ioEnable);
    return convertToMotionErrorCode(result);
}

// 设置IO脉冲状态
MotionErrorCode MotionAPI::setIOPulseState(short crd, short ioNum, short ioState) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_SetIOPluseState(m_conn.get(), crd, ioNum, ioState);
    return convertToMotionErrorCode(result);
}

// 设置IO脉冲触发
MotionErrorCode MotionAPI::setIOPulseTrigger(short crd, short ioNum, short ioTrigger) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_SetIOPluseTrigger(m_conn.get(), crd, ioNum, ioTrigger);
    return convertToMotionErrorCode(result);
}

// 获取FPGA版本
Optional<short> MotionAPI::getFpgaVersion() const {
    if (!m_conn.isValid()) {
        return Optional<short>::nullopt();
    }

    short version = 0;
    short result = API_GetFpgaVersion(m_conn.get(), version);

    if (result != convertToShort(MotionErrorCode::Success)) {
        return Optional<short>::nullopt();
    }

    return version;
}

// 发送速度IO点位
MotionErrorCode MotionAPI::sendSpeedIOPoint(short crd, const std::vector<SpeedIOPoint>& points) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    if (points.empty()) {
        return MotionErrorCode::ApiErrorPrm;
    }

    // 创建临时数组
    SpeedIOPoint* pointsArray = new SpeedIOPoint[points.size()];
    for (size_t i = 0; i < points.size(); i++) {
        pointsArray[i] = points[i];
    }

    short result = API_SendSpeedIO_Point(m_conn.get(), crd, static_cast<short>(points.size()), pointsArray);

    // 释放临时数组
    delete[] pointsArray;

    return convertToMotionErrorCode(result);
}

// 速度IO使能
MotionErrorCode MotionAPI::speedIOEnable(short crd, short enable, short ioNum) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_SpeedIO_Enable(m_conn.get(), crd, enable, ioNum);
    return convertToMotionErrorCode(result);
}

// 清除速度IO点位
MotionErrorCode MotionAPI::speedIOClearPoint(short crd) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    short result = API_SpeedIO_ClearPoint(m_conn.get(), crd);
    return convertToMotionErrorCode(result);
}



// 停止坐标系插补
MotionErrorCode MotionAPI::crdStop(short mask, short option, short stopType) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    // 由于API_CrdStop未定义，这里暂时返回成功
    // 实际项目中应该实现正确的停止函数
    return MotionErrorCode::Success;
}

// 暂停坐标系插补
MotionErrorCode MotionAPI::crdPause(short crd) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    // 由于API_CrdPause未定义，这里暂时返回成功
    // 实际项目中应该实现正确的暂停函数
    return MotionErrorCode::Success;
}



// 获取原始TADMotionConn指针
TADMotionConn* MotionAPI::getHandle() const {
    return m_conn.get();
}
