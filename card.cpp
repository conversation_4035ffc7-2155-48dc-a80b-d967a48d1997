﻿//#include <Windows.h>
#include <string.h>
#include <thread>
#include "math.h"
#include "cmdcode.h"
#include "ioctrl.h"
#include "crdmath.h"
#include "adconfig.h"
#include "ADMotionPackage.h"

#include "card.h"
#include "admc.h"
#include "ADMotionPackage.h"
#include "CardData.h"
#include "logger_proxy.h"
#include "logger_internal.h"

//初始化坐标系
void InitCrd(TADMotionConn* handle, short crd)
{
    auto pCard = handle->CardData;
    double currentPos[MAX_CRD_AXIS] = { 0 };

    MC_GetCrdPos(handle, crd, currentPos, MAX_CRD_AXIS);

    //衔接变量初始化
    pCard->gCrdApiPrm[crd].isFirstMinLine = true;

    // 使用范围for循环
    for (auto i = 0; i < MAX_CRD_AXIS; i++)
    {
        pCard->gCrdApiPrm[crd].pos[i]     = currentPos[i];
    }

    //对连续小线段缓冲和变量进行初始化
    for (auto i = 0; i < MAX_CRD; i++)
    {
            auto& lookAheadPrm = pCard->gLookAheadPrm[i];

            lookAheadPrm.nBufCount = 0;
            lookAheadPrm.nBufHead  = 0;
            lookAheadPrm.nBufRear  = 0;

            lookAheadPrm.lookAheadInitVel = 0;
            lookAheadPrm.endflag          = false;

            lookAheadPrm.enable = false;

            // 创建新的缓冲区
            lookAheadPrm.buffer = std::make_shared<LookAheadBuffer>(200);

            // 设置向后兼容的指针
            lookAheadPrm.pHostData     = lookAheadPrm.buffer->data();
            lookAheadPrm.pCmdBufInPtr  = lookAheadPrm.buffer->data();
            lookAheadPrm.pCmdBufOutPtr = lookAheadPrm.buffer->data();
        
    }
}
short MC_GetCrdPos(TADMotionConn* handle, short crd, double* pPos, short count) //获取count个坐标系位置
{
    TCardData* pCard = handle->CardData;
//    if (crd == 0)
//    {
//        pPos[0] = pCard->gCardAxisPos.X1Pos;
//        pPos[1] = pCard->gCardAxisPos.Y1Pos;
//    }
//    else if (crd == 1)
//    {
//        pPos[0] = pCard->gCardAxisPos.X2Pos;
//        pPos[1] = pCard->gCardAxisPos.Y2Pos;
//    }
//    else if (crd == 2)
//    {
//        pPos[0] = pCard->gCardAxisPos.X3Pos;
//        pPos[1] = pCard->gCardAxisPos.Y3Pos;
//    }
//    else if (crd == 3)
//    {
//        pPos[0] = pCard->gCardAxisPos.X4Pos;
//        pPos[1] = pCard->gCardAxisPos.Y4Pos;
//    }
    if (crd == 0)
    {
        pPos[0] = pCard->gCardAxisPos.X1Pos;
        pPos[1] = pCard->gCardAxisPos.Y1Pos;
    }
    else if (crd == 1)
    {
        pPos[0] = pCard->gCardAxisPos.X2Pos;
        pPos[1] = pCard->gCardAxisPos.Y2Pos;
    }
    return 0;
}

//获取反馈位置
short MC_GetfbCrdPos(TADMotionConn* handle, short crd, double* pPos, short count)
{
    TCardData* pCard = handle->CardData;
    if (crd == 0)
    {
        pPos[0] = pCard->gCardAxisPos.X2Pos;
        pPos[1] = pCard->gCardAxisPos.Y2Pos;
    }
    else if (crd == 1)
    {
        pPos[0] = pCard->gCardAxisPos.X4Pos;
        pPos[1] = pCard->gCardAxisPos.Y4Pos;
    }
    return 0;
}

short CrdPrmCheck(TADMotionConn* handle, short crd, double pos1, double pos2, double pos3, double pos4, double pos5, double pos6)
{
    if (RES_NONE != crd)
    {
        if ((crd < 0) || (crd > MAX_CRD))
        {
            return CMD_API_ERROR_OUT_RANGE;
        }
    }

    if (((pos1 > (int32_t)(0x3fffffff)) || (pos1 < (int32_t)(0xc0000000)))
        && ((pos2 > (int32_t)(0x3fffffff)) || (pos2 < (int32_t)(0xc0000000)))
        && ((pos3 > (int32_t)(0x3fffffff)) || (pos3 < (int32_t)(0xc0000000)))
        && ((pos4 > (int32_t)(0x3fffffff)) || (pos4 < (int32_t)(0xc0000000)))
        && ((pos5 > (int32_t)(0x3fffffff)) || (pos5 < (int32_t)(0xc0000000)))
        && ((pos6 > (int32_t)(0x3fffffff)) || (pos6 < (int32_t)(0xc0000000)))) //int32_t_Max一半，留出余量
    {
        return CMD_API_ERROR_PRM;
    }

    return CMD_SUCCESS;
}

short CrdRunCheck(TADMotionConn* handle, short crd) //主要是检查当前坐标系运行状态与FIFO的状态
{
    return CMD_SUCCESS;
}

short IsIntpType(short type)
{
    switch (type)
    {
        case CRD_CMD_TYPE_LINE:     //直线运动 0
        case CRD_CMD_TYPE_ARC:      //圆弧 1
        case CRD_CMD_TYPE_HELIX:    //平面螺旋线
        case CRD_CMD_TYPE_3DARC:    // 3D圆弧 3
        case CRD_CMD_TYPE_3DHELIX:  // 3维空间螺旋线 4
        case CRD_CMD_TYPE_BEZIER:   // Bezier插补
        case CRD_CMD_TYPE_G0LINE:   // G00运动
        case CRD_CMD_TYPE_FIVEAXIS: //五轴插补
        case CRD_CMD_TYPE_JUMP:
            break;
        default:
            return false;
    }
    return true;
}

short IsIntpLineOrArcType(short type)
{
    switch (type)
    {
        case CRD_CMD_TYPE_LINE: //直线运动 0
        case CRD_CMD_TYPE_ARC:  //圆弧 1
        case CRD_CMD_TYPE_3DARC:
            break;
        default:
            return false;
    }
    return true;
}

short CrdDataProcess(TADMotionConn* handle, short crd, TCrdData* pCrdData)
{
    short rtn;
    //计算运动数据的相关参数
    rtn = CrdPreProcessing(handle, crd, pCrdData); //数据发送前预处理
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }
    rtn = MC_insert_line_Processing(handle, crd, pCrdData);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }
    return CMD_SUCCESS;
}

void GetArcLength(double tempLength, short dimension, TCrdData* pCrdData)
{
    //劣弧圆心角
    auto length = tempLength / (2 * abs(int32_t(pCrdData->radius)));
    if (abs(length) > 1)
    {
        length = (length > 0) ? (1) : (-1);
    }
    //如果是优弧
    if (pCrdData->radius < 0)
    {
        pCrdData->radius = (-1) * pCrdData->radius;
        if (pCrdData->motionType == CRD_CMD_TYPE_ARC)
        { //平面圆弧
            pCrdData->length = 2 * pCrdData->radius * (PI - asin(length));
        }
        else if (pCrdData->motionType == CRD_CMD_TYPE_HELIX)
        { //空间圆弧
            auto n = short((pCrdData->pos[dimension] - pCrdData->startPos[dimension]) / pCrdData->pitch);
            pCrdData->length     = 2 * pCrdData->radius * (PI - asin(length) + PI * abs(n));
            pCrdData->motionType = CRD_CMD_TYPE_ARC;
        }
    }
    else
    {
        if (pCrdData->motionType == CRD_CMD_TYPE_ARC)
        { //平面圆弧
            pCrdData->length = pCrdData->radius * 2 * asin(length);
        }
        else if (pCrdData->motionType == CRD_CMD_TYPE_HELIX)
        { //空间圆弧
            auto n = short((pCrdData->pos[dimension] - pCrdData->startPos[dimension]) / pCrdData->pitch);
            pCrdData->length     = 2 * pCrdData->radius * (asin(length) + PI * abs(n));
            pCrdData->motionType = CRD_CMD_TYPE_ARC;
        }
    }
}

short CrdPreProcessing(TADMotionConn* handle, short crd, TCrdData* pCrdData) //获取起点，计算长度
{
    auto crdIndex = crd;
    double tempRadius;
    auto pCard = handle->CardData;

    if (nullptr == pCrdData)
    {
        return CMD_SUCCESS;
    }

    if (IsIntpType(pCrdData->motionType)) //在插补类型范围内
    {                                     //得到当前段的初始位置
        if (pCard->gCrdApiPrm[crd].isFirstMinLine == true)
        {
            auto rtn = MC_GetCrdPos(handle, crd, pCrdData->startPos, 2); //获取坐标系当前轴位置
            if (CMD_SUCCESS != rtn)
            {
                INTERNAL_LOG(handle, "CrdPreProcessing: MC_GetCrdPos failed, ret=%d", rtn);
                return rtn;
            }
            pCard->gCrdApiPrm[crdIndex].isFirstMinLine = false;
            INTERNAL_LOG(handle, "CrdPreProcessing: FirstMinLine is true,the startPos is = (%.3f , %.3f)", pCrdData->startPos[0],pCrdData->startPos[1]);
        }
        else
        {
            // 使用范围for循环
            for (auto i = 0; i < MAX_CRD_AXIS; i++)
            {
                pCrdData->startPos[i] = pCard->gCrdApiPrm[crd].pos[i]; //把终点作为起点
            }
        }
        //更新目标位置，根据输入的各个轴数据
        for (auto i = 0; i < MAX_CRD_AXIS; i++)
        {
                //pCrdData->pos[i] = pCard->gCrdApiPrm[crd].pos[i];
                pCard->gCrdApiPrm[crd].pos[i] = pCrdData->pos[i]; //把上一段终点存起来  下一段使用
        }
    }
    //多轴直线插补,或者五轴插补
    if ((pCrdData->motionType == CRD_CMD_TYPE_LINE) || (pCrdData->motionType == CRD_CMD_TYPE_G0LINE))
    {
        double tempL = 0;
        double axisLength[5]; //几何轴x,y,z轴
        for (auto i = 0; i < 5; i++)
        {
            axisLength[i] = pCrdData->pos[i] - pCrdData->startPos[i];
            tempL += axisLength[i] * axisLength[i];
        }
        pCrdData->length = sqrt(tempL);
        INTERNAL_LOG(handle, "CrdPreProcessing: Calculated line length = %.3f", pCrdData->length);
        //直线位移长度为0,不下发线段
        if (pCrdData->length <= ZERO)
        {
            //			return CMD_API_ERROR_CRD_LINE_ZERO;
            INTERNAL_LOG(handle, "CrdPreProcessing: Line length is <= zero, not sending line segment.");
            return 0; //不下发线段返回0，否则判断此次动作异常
        }
        for (auto i = 0; i < 3; i++)
        {
            pCrdData->startVector[i] = axisLength[i] / pCrdData->length;
        }
    }
    else if ((pCrdData->motionType == CRD_CMD_TYPE_ARC) || (pCrdData->motionType == CRD_CMD_TYPE_HELIX)) //平面圆弧插补
    {
        INTERNAL_LOG(handle, "CrdPreProcessing: Arc processing started for plane XY.");
        if (pCrdData->circlePlat == CRD_ARC_XY) //圆弧长度 xy平面
        {
            double startX = pCrdData->startPos[0];
            double startY = pCrdData->startPos[1];
            double endX   = pCrdData->pos[0];
            double endY   = pCrdData->pos[1];
            //假如传进来的半径为0；默认传入参数是圆心坐标，否则传入的是半径参数
            if (pCrdData->radius == 0)
            {
                INTERNAL_LOG(handle, "radius == 0,this is center arc.");
                double endVector[2], startVector[2], z;
                startVector[0] = pCrdData->startPos[0] - pCrdData->center[0];
                startVector[1] = pCrdData->startPos[1] - pCrdData->center[1];
                endVector[0]   = endX - pCrdData->center[0];
                endVector[1]   = endY - pCrdData->center[1];
                z = startVector[0] * endVector[1] - startVector[1] * endVector[0]; //大于0逆时针，＜0顺时针

                if (((pCrdData->circleDir == 0) && (z > 0)) || ((pCrdData->circleDir == 1) && (z < 0)))
                {
                    pCrdData->radius = (-1) * sqrt(endVector[0] * endVector[0] + endVector[1] * endVector[1]);
                }
                else
                {
                    pCrdData->radius = sqrt(endVector[0] * endVector[0] + endVector[1] * endVector[1]);
                }
                INTERNAL_LOG(handle, "CrdPreProcessing: Arc by center. Calculated radius = %.3f", pCrdData->radius);
                tempRadius = sqrt(startVector[0] * startVector[0] + startVector[1] * startVector[1]);
                if (abs_z(tempRadius - abs_z(pCrdData->radius)) > 1000) //给出0.5的误差大小
                {
                    //错误构建
                    INTERNAL_LOG(handle, "CrdPreProcessing: Arc by center. Radius error = %.3f,return CMD_API_ERROR_CRD_ARC_CENTER=%d", abs_z(tempRadius - abs_z(pCrdData->radius)),CMD_API_ERROR_CRD_ARC_CENTER);
                    INTERNAL_LOG(handle, "startPos[0] = %.3f, startPos[1] = %.3f", pCrdData->startPos[0], pCrdData->startPos[1]);
                    INTERNAL_LOG(handle, "center[0] = %.3f, center[1] = %.3f", pCrdData->center[0], pCrdData->center[1]);
                    INTERNAL_LOG(handle, "endX = %.3f, endY = %.3f", endX, endY);
                    return CMD_API_ERROR_CRD_ARC_CENTER;
                }
            }
            else
            { //计算圆心坐标
              //根据半径和其他信息计算圆心。设起点为A，终点为B，圆心为O，AB终点为M
                double startPoint[2], endPoint[2], center[2];
                startPoint[0] = startX;
                startPoint[1] = startY;
                endPoint[0]   = endX;
                endPoint[1]   = endY;
                //半径方式输入，起点和终点一样，则报错
                if ((startPoint[0] == endPoint[0]) && (startPoint[1] == endPoint[1]))
                {
                    INTERNAL_LOG(handle, "CrdPreProcessing: Arc by radius. Start and end points are the same,return CMD_API_ERROR_CRD_ARC_END_POS=%d",CMD_API_ERROR_CRD_ARC_END_POS);
                    return CMD_API_ERROR_CRD_ARC_END_POS;
                }
                GetArcCenter(startPoint,
                             endPoint,
                             center,
                             pCrdData->circleDir,
                             pCrdData->radius); // pCrdData->radius 大于0还是小于0 优弧和劣弧判断
                pCrdData->center[0] = center[0];
                pCrdData->center[1] = center[1];
                INTERNAL_LOG(handle, "CrdPreProcessing: Arc by radius. Calculated center = (%.3f, %.3f)", center[0], center[1]);
            }
            //起点到终点的弦长
            double tempLength = sqrt((endX - startX) * (endX - startX) + (endY - startY) * (endY - startY));
            if (0 != tempLength)
            {
                if (tempLength - (2 * abs_z(pCrdData->radius)) > 100)
                {
                    INTERNAL_LOG(handle, "CrdPreProcessing: Arc by radius. Radius error = %.3f,return CMD_API_ERROR_CRD_ARC_RADIUS=%d", abs_z(tempLength - (2 * abs_z(pCrdData->radius))),CMD_API_ERROR_CRD_ARC_RADIUS);
                    INTERNAL_LOG(handle, "startX = %.3f, startY = %.3f, endX = %.3f, endY = %.3f", startX, startY, endX, endY);
                    return CMD_API_ERROR_CRD_ARC_RADIUS; //错误构建
                }
                //计算弧长
                short dimension = 2; //空间圆弧法向
                GetArcLength(tempLength, dimension, pCrdData);
                INTERNAL_LOG(handle, "CrdPreProcessing: Calculated arc length = %.3f", pCrdData->length);
            }
            else
            {
                //整圆
                INTERNAL_LOG(handle, "CrdPreProcessing: Arc by radius. Whole circle");
                pCrdData->radius = abs_z(pCrdData->radius);
                pCrdData->length = 2 * PI * pCrdData->radius;
            }
            //计算切矢量
            double startVector[2], endVector[2], tempStartVector[2], tempEndVector[2];
            startVector[0] = startX - pCrdData->center[0];
            startVector[1] = startY - pCrdData->center[1];
            endVector[0]   = endX - pCrdData->center[0];
            endVector[1]   = endY - pCrdData->center[1];
            //如果是顺时针
            if (pCrdData->circleDir == 0)
            { //注意叉乘是 平面向量叉乘径向向量 （先后顺序）
                tempStartVector[0] = startVector[1];
                tempStartVector[1] = -startVector[0];
                tempEndVector[0]   = endVector[1];
                tempEndVector[1]   = -endVector[0];
            }
            else // 如果是逆时针
            {
                tempStartVector[0] = -startVector[1];
                tempStartVector[1] = startVector[0];
                tempEndVector[0]   = -endVector[1];
                tempEndVector[1]   = endVector[0];
            }
            pCrdData->startVector[0] = tempStartVector[0] / pCrdData->radius;
            pCrdData->startVector[1] = tempStartVector[1] / pCrdData->radius;
            pCrdData->startVector[2] = 0;
            pCrdData->endVector[0]   = tempEndVector[0] / pCrdData->radius;
            pCrdData->endVector[1]   = tempEndVector[1] / pCrdData->radius;
            pCrdData->endVector[2]   = 0;
        }
        else if (pCrdData->circlePlat == CRD_ARC_YZ) //圆弧长度 yz平面
        {
            double startY = pCrdData->startPos[1];
            double startZ = pCrdData->startPos[2];
            double endY   = pCrdData->pos[1];
            double endZ   = pCrdData->pos[2];
            //假如传进来的半径为0；默认传入参数是圆心坐标，否则传入的是半径参数
            if (pCrdData->radius == 0)
            {
                double endVector[2], startVector[2], x;
                startVector[0] = pCrdData->startPos[1] - pCrdData->center[1];
                startVector[1] = pCrdData->startPos[2] - pCrdData->center[2];
                endVector[0]   = endY - pCrdData->center[1];
                endVector[1]   = endZ - pCrdData->center[2];
                x = startVector[0] * endVector[1] - startVector[1] * endVector[0]; //大于0逆时针，＜0顺时针
                if (((pCrdData->circleDir == 0) && (x > 0)) || ((pCrdData->circleDir == 1) && (x < 0)))
                {
                    pCrdData->radius = (-1) * sqrt(endVector[0] * endVector[0] + endVector[1] * endVector[1]);
                }
                else
                {
                    pCrdData->radius = sqrt(endVector[0] * endVector[0] + endVector[1] * endVector[1]);
                }
                tempRadius = sqrt(startVector[0] * startVector[0] + startVector[1] * startVector[1]);
                if (abs_z(tempRadius - abs_z(pCrdData->radius)) > 0.5)
                {
                    //错误构建
                    return CMD_API_ERROR_CRD_ARC_CENTER;
                }
            }
            else
            { //计算圆心坐标
              //根据半径和其他信息计算圆心。设起点为A，终点为B，圆心为O，AB终点为M
                double startPoint[2], endPoint[2], center[2];
                startPoint[0] = startY;
                startPoint[1] = startZ;
                endPoint[0]   = endY;
                endPoint[1]   = endZ;
                //半径方式输入，起点和终点一样，则报错
                if ((startPoint[0] == endPoint[0]) && (startPoint[1] == endPoint[1]))
                {
                    return CMD_API_ERROR_CRD_ARC_END_POS;
                }
                GetArcCenter(startPoint, endPoint, center, pCrdData->circleDir, pCrdData->radius);
                pCrdData->center[1] = center[0];
                pCrdData->center[2] = center[1];
            }
            //起点到终点的弦长
            double tempLength = sqrt((endY - startY) * (endY - startY) + (endZ - startZ) * (endZ - startZ));
            if (0 != tempLength)
            {
                if (tempLength > (2 * abs_z(pCrdData->radius)))
                {
                    return CMD_API_ERROR_CRD_ARC_RADIUS; //错误构建，TODO.....
                }
                //计算弧长
                short dimension = 0; //空间圆弧法向
                GetArcLength(tempLength, dimension, pCrdData);
            }
            else
            {
                //整圆
                pCrdData->radius = abs_z(pCrdData->radius);
                pCrdData->length = 2 * PI * pCrdData->radius;
            }

            //计算切矢量
            double startVector[2], endVector[2], tempStartVector[2], tempEndVector[2];
            startVector[0] = startY - pCrdData->center[1];
            startVector[1] = startZ - pCrdData->center[2];
            endVector[0]   = endY - pCrdData->center[1];
            endVector[1]   = endZ - pCrdData->center[2];
            //如果是顺时针
            if (pCrdData->circleDir == 0)
            { //注意叉乘是 平面向量叉乘径向向量 （先后顺序）
                tempStartVector[0] = startVector[1];
                tempStartVector[1] = -startVector[0];

                tempEndVector[0] = endVector[1];
                tempEndVector[1] = -endVector[0];
            }
            else
            { // 如果是逆时针
                tempStartVector[0] = -startVector[1];
                tempStartVector[1] = startVector[0];

                tempEndVector[0] = -endVector[1];
                tempEndVector[1] = endVector[0];
            }
            pCrdData->startVector[1] = tempStartVector[0] / pCrdData->radius;
            pCrdData->startVector[2] = tempStartVector[1] / pCrdData->radius;
            pCrdData->startVector[0] = 0;
            pCrdData->endVector[1]   = tempEndVector[0] / pCrdData->radius;
            pCrdData->endVector[2]   = tempEndVector[1] / pCrdData->radius;
            pCrdData->endVector[0]   = 0;
        }
        else if (pCrdData->circlePlat == CRD_ARC_ZX) //圆弧长度 zx平面
        {
            double startZ = pCrdData->startPos[2];
            double startX = pCrdData->startPos[0];
            double endZ   = pCrdData->pos[2];
            double endX   = pCrdData->pos[0];
            //假如传进来的半径为0；默认传入参数是圆心坐标，否则传入的是半径参数
            if (pCrdData->radius == 0)
            {
                double endVector[2], startVector[2], y;
                startVector[0] = pCrdData->startPos[2] - pCrdData->center[2];
                startVector[1] = pCrdData->startPos[0] - pCrdData->center[0];
                endVector[0]   = endZ - pCrdData->center[2];
                endVector[1]   = endX - pCrdData->center[0];
                y = startVector[0] * endVector[1] - startVector[1] * endVector[0]; //大于0逆时针，＜0顺时针

                if (((pCrdData->circleDir == 0) && (y > 0)) || ((pCrdData->circleDir == 1) && (y < 0)))
                {
                    pCrdData->radius = (-1) * sqrt(endVector[0] * endVector[0] + endVector[1] * endVector[1]);
                }
                else
                {
                    pCrdData->radius = sqrt(endVector[0] * endVector[0] + endVector[1] * endVector[1]);
                }
                tempRadius = sqrt(startVector[0] * startVector[0] + startVector[1] * startVector[1]);
                if (abs_z(tempRadius - abs_z(pCrdData->radius)) > 0.5)
                {
                    //错误构建
                    return CMD_API_ERROR_CRD_ARC_CENTER;
                }
            }
            else
            { //计算圆心坐标,根据半径和其他信息计算圆心。设起点为A，终点为B，圆心为O，AB终点为M
                double startPoint[2], endPoint[2], center[2];
                startPoint[0] = startZ;
                startPoint[1] = startX;
                endPoint[0]   = endZ;
                endPoint[1]   = endX;
                //半径方式输入，起点和终点一样，则报错
                if ((startPoint[0] == endPoint[0]) && (startPoint[1] == endPoint[1]))
                {
                    return CMD_API_ERROR_CRD_ARC_END_POS;
                }
                GetArcCenter(startPoint, endPoint, center, pCrdData->circleDir, pCrdData->radius);
                pCrdData->center[2] = center[0];
                pCrdData->center[0] = center[1];
            }
            //起点到终点的弦长
            double tempLength = sqrt((endZ - startZ) * (endZ - startZ) + (endX - startX) * (endX - startX));
            if (0 != tempLength)
            {
                if (tempLength > (2 * abs_z(pCrdData->radius)))
                {
                    return CMD_API_ERROR_CRD_ARC_RADIUS; //错误构建，TODO.....
                }
                //计算弧长
                short dimension = 1; //空间圆弧法向
                GetArcLength(tempLength, dimension, pCrdData);
            }
            else
            {
                //整圆
                pCrdData->radius = abs_z(pCrdData->radius);
                pCrdData->length = 2 * PI * pCrdData->radius;
            }
            //计算切矢量
            double startVector[2], endVector[2], tempStartVector[2], tempEndVector[2];
            startVector[0] = startZ - pCrdData->center[2];
            startVector[1] = startX - pCrdData->center[0];
            endVector[0]   = endZ - pCrdData->center[2];
            endVector[1]   = endX - pCrdData->center[0];
            //如果是顺时针
            if (pCrdData->circleDir == 0)
            { //注意叉乘是 平面向量叉乘径向向量 （先后顺序）
                tempStartVector[0] = startVector[1];
                tempStartVector[1] = -startVector[0];
                tempEndVector[0]   = endVector[1];
                tempEndVector[1]   = -endVector[0];
            }
            else
            { // 如果是逆时针
                tempStartVector[0] = -startVector[1];
                tempStartVector[1] = startVector[0];
                tempEndVector[0]   = -endVector[1];
                tempEndVector[1]   = endVector[0];
            }
            pCrdData->startVector[2] = tempStartVector[0] / pCrdData->radius;
            pCrdData->startVector[0] = tempStartVector[1] / pCrdData->radius;
            pCrdData->startVector[1] = 0;
            pCrdData->endVector[2]   = tempEndVector[0] / pCrdData->radius;
            pCrdData->endVector[0]   = tempEndVector[1] / pCrdData->radius;
            pCrdData->endVector[1]   = 0;
        }
    }
    else if ((pCrdData->motionType == CRD_CMD_TYPE_3DARC) || (pCrdData->motionType == CRD_CMD_TYPE_3DHELIX))
    {
        //空间圆弧处理
        //求空间圆心坐标 起点A,中间点C，结束点B。
        double AC[3];
        double BC[3];
        for (int i = 0; i < 3; i++)
        {
            AC[i] = pCrdData->midPoint[i] - pCrdData->startPos[i];
            BC[i] = pCrdData->midPoint[i] - pCrdData->pos[i];
        }
        double platVector[3]; //平面法向量
        GetVectorCross(BC, AC, platVector);
        double norm = GetVectorNorm(platVector); //向量模
        if (norm < 1.0e-11)                      // S,M,E共线，无法构造圆
        {
            return CMD_API_ERROR_CRD_ARC3D_COLLINEAR;
        }
        //转为单位向量
        platVector[0] = platVector[0] / norm;
        platVector[1] = platVector[1] / norm;
        platVector[2] = platVector[2] / norm;

        //求AC和BC的中点D和E分别与中间点组成的向量
        double DC[3], EC[3];
        DC[0] = AC[0] * 0.5;
        DC[1] = AC[1] * 0.5;
        DC[2] = AC[2] * 0.5;
        EC[0] = BC[0] * 0.5;
        EC[1] = BC[1] * 0.5;
        EC[2] = BC[2] * 0.5;

        double m = GetVectorDot(AC, DC);
        double n = GetVectorDot(BC, EC);
        double p = GetVectorDot(AC, AC);
        double q = GetVectorDot(BC, BC);
        double w = GetVectorDot(AC, BC);

        double s = (n * p - m * w) / (w * w - p * q);
        double t = (m * q - n * w) / (w * w - p * q);

        pCrdData->center[0] = pCrdData->midPoint[0] + AC[0] * t + BC[0] * s; //圆心坐标
        pCrdData->center[1] = pCrdData->midPoint[1] + AC[1] * t + BC[1] * s; //圆心坐标
        pCrdData->center[2] = pCrdData->midPoint[2] + AC[2] * t + BC[2] * s; //圆心坐标
                                                                             //求半径
        double startRadiusDir[3], endRadiusDir[3];
        double startTangent[3], endTangent[3];
        double angle;
        startRadiusDir[0] = pCrdData->startPos[0] - pCrdData->center[0];
        startRadiusDir[1] = pCrdData->startPos[1] - pCrdData->center[1];
        startRadiusDir[2] = pCrdData->startPos[2] - pCrdData->center[2];

        pCrdData->radius = GetVectorNorm(startRadiusDir); //模
                                                          //如果起点和终点方向重合，则圆弧为零度圆弧
        if (pCrdData->radius > 1.0e-6)
        {
            double radiusReciprocal = 1.0 / pCrdData->radius;
            for (int i = 0; i < 3; i++)
            {
                startRadiusDir[i] = startRadiusDir[i] * radiusReciprocal;
                endRadiusDir[i]   = ((double)pCrdData->pos[i] - pCrdData->center[i]) * radiusReciprocal;
            }
            GetVectorCross(platVector, startRadiusDir, startTangent);
            GetVectorCross(platVector, endRadiusDir, endTangent);
            //计算圆弧张角余弦参数
            double tempParam = GetVectorDot(startRadiusDir, endRadiusDir);
            //起点半径方向与终点半径方向的叉积，如果与法向量方向相同，则张角小于180；否则，张角大于180
            double cross[3];
            GetVectorCross(startRadiusDir, endRadiusDir, cross);
            double crossNorm = GetVectorDot(platVector, cross);
            if (crossNorm > 0.0)
            {
                angle = acos(tempParam);
            }
            else if (crossNorm < 0.0)
            {
                angle = acos(-tempParam) + PI;
            }
            else
            {
                angle = PI;
            }
        }
        else
        {
            //半径太小
            return CMD_API_ERROR_CRD_ARC3D_RADIUS_SMALL;
        }
        pCrdData->startVector[0] = startTangent[0];
        pCrdData->startVector[1] = startTangent[1];
        pCrdData->startVector[2] = startTangent[2];
        pCrdData->endVector[0]   = endTangent[0];
        pCrdData->endVector[1]   = endTangent[1];
        pCrdData->endVector[2]   = endTangent[2];

        double vectorX[3];
        // platVector叉乘Y 为-X
        GetVectorCross(platVector, startRadiusDir, vectorX);
        // vectorX和startTangent同向顺时针，反向逆时针
        double dirParam = GetVectorDot(startTangent, vectorX);
        if (dirParam > 0)
        {
            pCrdData->circleDir = 0;
        }
        else
        {
            pCrdData->circleDir = 1;
        }

        pCrdData->crdTrans[0] = vectorX[0];
        pCrdData->crdTrans[1] = startRadiusDir[0];
        pCrdData->crdTrans[2] = platVector[0];
        pCrdData->crdTrans[3] = vectorX[1];
        pCrdData->crdTrans[4] = startRadiusDir[1];
        pCrdData->crdTrans[5] = platVector[1];
        pCrdData->crdTrans[6] = vectorX[2];
        pCrdData->crdTrans[7] = startRadiusDir[2];
        pCrdData->crdTrans[8] = platVector[2];
        //计算圆弧弧长
        if (pCrdData->motionType == CRD_CMD_TYPE_3DARC)
        {
            pCrdData->length = angle * pCrdData->radius;
        }
        else if (pCrdData->motionType == CRD_CMD_TYPE_3DHELIX) //空间螺线。
        {
            short  n         = (short)(abs_z(pCrdData->height) / pCrdData->pitch); //加绝对值，否则长度为负值
            double allAngle  = angle + (n * 2 * PI);
            pCrdData->length = allAngle * pCrdData->radius;
            //空间螺旋线目标位置计算,赋值给pCrdData->pos。
            double currentPos[3];
            //顺时针
            if (pCrdData->circleDir == 0)
            {
                currentPos[0] = 0 * cos(allAngle) + pCrdData->radius * sin(allAngle);
                currentPos[1] = (-1) * 0 * sin(allAngle) + pCrdData->radius * cos(allAngle);
            }
            else
            { //逆时针
                currentPos[0] = 0 * cos(allAngle) - pCrdData->radius * sin(allAngle);
                currentPos[1] = 0 * sin(allAngle) + pCrdData->radius * cos(allAngle);
            }
            //平面坐标映射到空间坐标
            double a  = pCrdData->crdTrans[1] * currentPos[1];
            double b  = pCrdData->crdTrans[0] * currentPos[0];
            double c  = pCrdData->crdTrans[2] * pCrdData->height;
            double a1 = pCrdData->crdTrans[3] * currentPos[0];
            double b1 = pCrdData->crdTrans[4] * currentPos[1];
            double c1 = pCrdData->crdTrans[5] * pCrdData->height;
            double a2 = pCrdData->crdTrans[6] * currentPos[0];
            double b2 = pCrdData->crdTrans[7] * currentPos[1];
            double c2 = pCrdData->crdTrans[8] * pCrdData->height;

            pCrdData->pos[0] = a + b + c + pCrdData->center[0];
            pCrdData->pos[1] = a1 + b1 + c1 + pCrdData->center[1];
            pCrdData->pos[2] = a2 + b2 + c2 + pCrdData->center[2];
        }
    }
    if (IsIntpType(pCrdData->motionType))
    {
        for (int i = 0; i < MAX_CRD_AXIS; i++)
        {
            pCard->gCrdApiPrm[crdIndex].pos[i] = pCrdData->pos[i];
        }
    }
    return CMD_SUCCESS;
}



short MC_SetCrdPrm(TADMotionConn* handle, short crd, TCrdPrm* pCrdPrm) // 003
{
    g_Log(handle,
          LOG_DEBUG,
          "MC_SetCrdPrm function  in");
    short i, rtn;
    if ((crd < 0) || (crd > MAX_CRD)) // A
    {
        return CMD_API_ERROR_OUT_RANGE;
    }
    if ((pCrdPrm->synVelMax <= 0) || (pCrdPrm->synAccMax <= 0))
    {
        g_Log(handle,
              LOG_ERROR,
              "MC_SetCrdPrm: synVelMax error");
        return CMD_API_ERROR_PRM;
    }
    if (pCrdPrm->synVelMax < pCrdPrm->synAccMax)
    {
        return CMD_API_ERROR_PRM;
    }

    unsigned short tick = GetIndex(handle);

    CommandInitial(handle, tick, SET_CRD_PRM);
    Add16ToBuff(handle, tick, crd);
    Add32Fix16ToBuff(handle, tick, pCrdPrm->synVelMax);
    Add32Fix16ToBuff(handle, tick, pCrdPrm->synAccMax);
    Add16ToBuff(handle, tick, pCrdPrm->setOriginFlag);

    for (i = 0; i < MAX_PROFILE; i++)
    {
        Add32ToBuff(handle, tick, pCrdPrm->originPos[i]);
    }

    rtn = SendCommand(handle, tick);
    if (CMD_SUCCESS != rtn)
    {
        CommandUninitial(handle, tick);
        g_Log(handle,
              LOG_ERROR,
              "MC_SetCrdPrm: return error,the error code is %d",rtn);
        return rtn;
    }
    g_Log(handle,
          LOG_INFO,
          "MC_SetCrdPrm: Set params for crd=%d. synVelMax=%.2f, synAccMax=%.2f",
          crd,
          pCrdPrm->synVelMax,
          pCrdPrm->synAccMax);
    InitCrd(handle, crd); //该指令要在DSP设置完currentpos之后调用

    CommandUninitial(handle, tick);
    return CMD_SUCCESS;
}

short MC_GetCrdPrm(TADMotionConn* handle, short crd, TCrdPrm* pCrdPrm) //获取坐标系参数
{
    short i, rtn;

    if ((crd < 1) || (crd > MAX_CRD))
    {
        return CMD_API_ERROR_OUT_RANGE;
    }

    unsigned short tick = GetIndex(handle);

    CommandInitial(handle, tick, GET_CRD_PRM);
    Add16ToBuff(handle, tick, crd);
    rtn = SendCommand(handle, tick);
    if (CMD_SUCCESS != rtn)
    {
        CommandUninitial(handle, tick);
        return rtn;
    }
    for (i = 0; i < MAX_CRD_AXIS; i++)
    {
        Get16FromBuff(handle, tick, &pCrdPrm->axisMap[i]);
    }
    for (i = 0; i < MAX_CRD_AXIS; i++)
    {
        Get32Fix16FromBuff(handle, tick, &pCrdPrm->axisVelMax[i]);
    }
    Get32Fix16FromBuff(handle, tick, &pCrdPrm->synVelMax);
    Get32Fix16FromBuff(handle, tick, &pCrdPrm->synAccMax);
    Get32Fix16FromBuff(handle, tick, &pCrdPrm->decAbruptStop);
    Get32Fix16FromBuff(handle, tick, &pCrdPrm->decSmoothStop);
    Get16FromBuff(handle, tick, &pCrdPrm->setOriginFlag);
    for (i = 0; i < MAX_CRD_AXIS; i++)
    {
        Get32FromBuff(handle, tick, &pCrdPrm->originPos[i]);
    }
    CommandUninitial(handle, tick);
    return CMD_SUCCESS;
}

short MC_CrdClear(TADMotionConn* handle, short crd)
{
    short rtn, crdIndex;

    rtn = CrdPrmCheck(handle, crd, 0, 0, 0, 0, 0, 0);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }
    TCardData*     pCard = handle->CardData;
    unsigned short tick  = GetIndex(handle);
    CommandInitial(handle, tick, CRD_CLEAR);
    Add16ToBuff(handle, tick, crd);
    rtn = SendCommand(handle, tick);
    if (CMD_SUCCESS != rtn)
    {
        CommandUninitial(handle, tick);
        return rtn;
    }

    crdIndex = crd - 1;

    //衔接变量初始化
    pCard->gCrdApiPrm[crdIndex].isFirstMinLine = TRUE;

    CommandUninitial(handle, tick);
    return CMD_SUCCESS;
}

short MC_CrdStart(TADMotionConn* handle, short crd)
{
    short rtn = 0;
    g_Log(handle, LOG_INFO, "MC_CrdStart: Trajectory execution started for crd=%d.", crd);
    if(handle->CardData->gLookAheadPrm[crd].enable == TRUE)
    {
        rtn = MC_CloseLookAhead(handle, crd);
    }
    if(rtn != CMD_SUCCESS)
    {
        return rtn;
    }
    TCardData* pCard = handle->CardData;

    pCard->gCrdApiPrm[crd].isFirstMinLine = TRUE;
    InitCrd(handle, crd);
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, CRD_START);

    Add16ToBuff(handle, tick, crd);

    short ret = SendCommand(handle, tick);
    CommandUninitial(handle, tick);
    return ret;
}

short MC_CrdStop(TADMotionConn* handle, short crd)
{
    unsigned short tick = GetIndex(handle);

    CommandInitial(handle, tick, CRD_STOP);

   Add16ToBuff(handle, tick, crd);
   short stopType = 2; //1 :紧急停止  2:平滑停止
   Add16ToBuff(handle, tick, stopType);
    short ret = SendCommand(handle, tick);
    CommandUninitial(handle, tick);
    InitCrd(handle, crd);
    return ret;
}


short MC_CrdPause(TADMotionConn* handle, short crd)
{
    short rtn;
    rtn = CrdPrmCheck(handle, crd, 0, 0, 0, 0, 0, 0);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, CRD_PAUSE);

    Add16ToBuff(handle, tick, crd);

    short ret = SendCommand(handle, tick);
    CommandUninitial(handle, tick);
    return ret;
}

short MC_CrdData(TADMotionConn* handle, short crd, TCrdData* pCrdData)
{
    short rtn;
    rtn = CrdPrmCheck(handle, crd, 0, 0, 0, 0, 0, 0);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }

    rtn = CrdRunCheck(handle, crd);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }

    rtn = CrdDataProcess(handle, crd, pCrdData);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }
    return CMD_SUCCESS;
}



//发送插补数据到dsp
short SendCrdData(TADMotionConn* handle, short crd, TCrdData* pCrdData)
{
    short i;

    if (nullptr == pCrdData)
    {
        return CMD_API_ERROR_POINTER;
    }

    unsigned short tick = GetIndex(handle);

    CommandInitial(handle, tick, SEND_CRD_DATA);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, pCrdData->motionType); //直线/圆弧
    Add16ToBuff(handle, tick, 1);//插补类型  0：T型,1：S型

    Add16ToBuff(handle, tick, pCrdData->circlePlat);
    //起点位置
    for (i = 0; i < MAX_PROFILE; i++)
    {
        Add64Fix16ToBuff(handle, tick, pCrdData->startPos[i]);
    }
    //终点位置
    for (i = 0; i < MAX_PROFILE; i++)
    {
        Add64Fix16ToBuff(handle, tick, pCrdData->pos[i]);
    }
    Add64Fix16ToBuff(handle, tick, pCrdData->radius);
    Add16ToBuff(handle, tick, pCrdData->circleDir);
    for (i = 0; i < MAX_PROFILE; i++)
    {
        Add64Fix16ToBuff(handle, tick, pCrdData->center[i]);
    }
    Add32Fix16ToBuff(handle, tick, pCrdData->vel);
    Add32Fix16ToBuff(handle, tick, pCrdData->acc);
    Add32Fix16ToBuff(handle, tick, pCrdData->acc); // dec
    Add32Fix16ToBuff(handle, tick, pCrdData->velStart);//起点速度

    Add32Fix16ToBuff(handle, tick, pCrdData->velEnd);
    Add64Fix16ToBuff(handle, tick, pCrdData->length);

    INTERNAL_LOG(handle,
                 "SendCrdData: crd=%d, startPos[0]=%.2f, startPos[1]=%.2f, pos[0]=%.2f, pos[1]=%.2f, vel=%.2f, acc=%.2f, length=%.2f",
                 crd,
                 pCrdData->startPos[0],
                 pCrdData->startPos[1],
                 pCrdData->pos[0],
                 pCrdData->pos[1],
                 pCrdData->vel,
                 pCrdData->acc,
                 pCrdData->length);

    short ret = SendCommand(handle, tick);
    CommandUninitial(handle, tick);
    return ret;
}

short MC_CrdLn(TADMotionConn* handle,
               short          lnType,
               short          isG0,
               short          crd,
               int32_t        x,
               int32_t        y,
               int32_t        z,
               int32_t        a,
               int32_t        b,
               double         synVel,
               double         synAcc,
               double         velEnd)
{
    short    rtn;
    TCrdData tempCrd;
    rtn = CrdPrmCheck(handle, crd, x, y, z, a, b, 0);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }
    if (synVel < synAcc)
    {
        return CMD_API_ERROR_PRM;
    }
    memset((void*)&tempCrd, 0, sizeof(TCrdData));

    tempCrd.motionType = CRD_CMD_TYPE_LINE; // 运动类型,0:直线插补
    tempCrd.circlePlat = 0;                 // 圆弧插补的平面
    switch (lnType)
    {
        case API_CMD_LN_XY:
            tempCrd.active[0] = true; // 当前输入的哪几个轴数据
            tempCrd.active[1] = true;
            tempCrd.active[2] = false;
            tempCrd.active[3] = false;
            tempCrd.active[4] = false;
            tempCrd.pos[0]    = x;
            tempCrd.pos[1]    = y;
            tempCrd.pos[2]    = 0;
            tempCrd.pos[3]    = 0;
            tempCrd.pos[4]    = 0;
            break;
        case API_CMD_LN_XYZ:
            break;
        case API_CMD_LN_XYZA:
            break;
        case API_CMD_LN_XYZAB:
            break;
        default:
            return CMD_API_ERROR_TYPE;
    }
    tempCrd.radius    = 0; // 圆弧插补的半径
    tempCrd.circleDir = 0; // 圆弧旋转方向,0:顺时针;1:逆时针
    tempCrd.center[0] = 0; // 圆弧插补的圆心坐标
    tempCrd.center[1] = 0;
    tempCrd.center[2] = 0;
    tempCrd.vel       = synVel; // 当前段合成目标速度
    tempCrd.acc       = synAcc; // 当前段合成加速度
    g_Log(handle, LOG_INFO, "MC_CrdLn: crd=%d, x=%d, y=%d, synVel=%.2f, synAcc=%.2f", crd, x, y, synVel, synAcc);
    if (TRUE == isG0)
    {
        tempCrd.velEnd = 0;
    }
    else
    {
        tempCrd.velEnd = velEnd; // 当前段合成终点速度
    }

    rtn = CrdDataProcess(handle, crd, &tempCrd);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }
    return CMD_SUCCESS;
}

short MC_CrdArc3Point(TADMotionConn* handle,
                      short          arcPlat,
                      short          crd,
                      int32_t*       p1, // size = 3
                      int32_t*       p2, // size = 3
                      int32_t*       p3, // size = 3
                      double         synVel,
                      double         synAcc,
                      double         velEnd)
{
    short    rtn;
    int32_t x = p3[0];
    int32_t y = p3[1];
    int32_t z = p3[2];
    double  Center[2];
    short  circleDir1 = 0;
    rtn                = CalArcCenter_dir(p1, p2, p3, Center, &circleDir1);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }
    double posNew[2] = {0,0};
    rtn =   API_GetCrdPos(handle, crd, posNew);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }
    double length = sqrt((p1[0]-posNew[0])*(p1[0]-posNew[0]) + (p1[1]-posNew[1])*(p1[1]-posNew[1]));
    if(length > 10)
    {
        INTERNAL_LOG(handle, "MC_CrdArc3Point,length > 50 : length = %.3f,add a line", length);
        rtn = MC_CrdLn(handle, API_CMD_LN_XY, false, crd, p1[0], p1[1], 0, 0, 0, synVel, synAcc, 0);
        if (CMD_SUCCESS != rtn)
        {
            INTERNAL_LOG(handle, "MC_CrdArc3Point: MC_CrdLn failed, ret=%d", rtn);
            return rtn;
        }
    }
    return MC_CrdArcC(handle, arcPlat, crd, x, y, z, Center[0], Center[1], 0, circleDir1, synVel, synAcc, velEnd);
}

short MC_CrdArcR(TADMotionConn* handle,
                 short          arcPlat,
                 short          crd,
                 int32_t        x,
                 int32_t        y,
                 int32_t        z,
                 double         radius,
                 short          circleDir,
                 double         synVel,
                 double         synAcc,
                 double         velEnd)
{
    short    rtn;
    TCrdData tempCrd;
    rtn = CrdPrmCheck(handle, crd, x, y, z, radius, 0, 0);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }

    rtn = CrdRunCheck(handle, crd);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }

    memset((void*)&tempCrd, 0, sizeof(TCrdData));
    tempCrd.motionType = CRD_CMD_TYPE_ARC; // 运动类型 1:圆弧插补
    switch (arcPlat)
    {
        case API_CMD_ARC_XY:
            tempCrd.circlePlat = CRD_ARC_XY; // 圆弧插补的平面  0:xy 1:yz 2:zx
            tempCrd.active[0]  = TRUE;       // 当前输入的哪几个轴数据
            tempCrd.active[1]  = TRUE;
            tempCrd.active[2]  = FALSE;
            tempCrd.active[3]  = FALSE;
            tempCrd.active[4]  = FALSE;
            tempCrd.pos[0]     = x; // 当前段各轴终点位置
            tempCrd.pos[1]     = y;
            tempCrd.pos[2]     = 0;
            tempCrd.pos[3]     = 0;
            tempCrd.pos[4]     = 0;
            break;
        case API_CMD_ARC_YZ:
            tempCrd.circlePlat = CRD_ARC_YZ; // 圆弧插补的平面  0:xy 1:yz 2:zx
            tempCrd.active[0]  = FALSE;      // 当前输入的哪几个轴数据
            tempCrd.active[1]  = TRUE;
            tempCrd.active[2]  = TRUE;
            tempCrd.active[3]  = FALSE;
            tempCrd.active[4]  = FALSE;
            tempCrd.pos[0]     = 0; // 当前段各轴终点位置
            tempCrd.pos[1]     = y;
            tempCrd.pos[2]     = z;
            tempCrd.pos[3]     = 0;
            tempCrd.pos[4]     = 0;
            break;
        case API_CMD_ARC_ZX:
            tempCrd.circlePlat = CRD_ARC_ZX; // 圆弧插补的平面  0:xy 1:yz 2:zx
            tempCrd.active[0]  = TRUE;       // 当前输入的哪几个轴数据
            tempCrd.active[1]  = FALSE;
            tempCrd.active[2]  = TRUE;
            tempCrd.active[3]  = FALSE;
            tempCrd.active[4]  = FALSE;
            tempCrd.pos[0]     = x; // 当前段各轴终点位置
            tempCrd.pos[1]     = 0;
            tempCrd.pos[2]     = z;
            tempCrd.pos[3]     = 0;
            tempCrd.pos[4]     = 0;
            break;
        default:
            return CMD_API_ERROR_TYPE;
    }

    tempCrd.radius    = radius;    // 圆弧插补的半径
    tempCrd.circleDir = circleDir; // 圆弧旋转方向,0:顺时针;1:逆时针
    tempCrd.center[0] = 0;         // 圆弧插补的圆心坐标
    tempCrd.center[1] = 0;
    tempCrd.center[2] = 0;
    tempCrd.vel       = synVel; // 当前段合成目标速度
    tempCrd.acc       = synAcc; // 当前段合成加速度
    tempCrd.velEnd    = velEnd; // 当前段合成终点速度

    rtn = CrdDataProcess(handle, crd, &tempCrd);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }
    return CMD_SUCCESS;
}

short MC_CrdArcC(TADMotionConn* handle,
                 short          arcPlat,
                 short          crd,
                 int32_t        x,
                 int32_t        y,
                 int32_t        z,
                 double         xCenter,
                 double         yCenter,
                 double         zCenter,
                 short          circleDir,
                 double         synVel,
                 double         synAcc,
                 double         velEnd)
{ //
    short    rtn;
    TCrdData tempCrd;
    rtn = CrdPrmCheck(handle, crd, x, y, z, xCenter, yCenter, zCenter);
    if (CMD_SUCCESS != rtn)
    {
        INTERNAL_LOG(handle, "MC_CrdArcC: CrdPrmCheck failed, ret=%d", rtn);
        return rtn;
    }

    rtn = CrdRunCheck(handle, crd);
    if (CMD_SUCCESS != rtn)
    {
        INTERNAL_LOG(handle, "MC_CrdArcC: CrdRunCheck failed, ret=%d", rtn);
        return rtn;
    }


    memset((void*)&tempCrd, 0, sizeof(TCrdData));
    tempCrd.motionType = CRD_CMD_TYPE_ARC; // 运动类型 1:圆弧插补
    switch (arcPlat)
    {
        case API_CMD_ARC_XY:
            tempCrd.circlePlat = CRD_ARC_XY; // 圆弧插补的平面  0:xy 1:yz 2:zx
            tempCrd.active[0]  = TRUE;       // 当前输入的哪几个轴数据
            tempCrd.active[1]  = TRUE;
            tempCrd.active[2]  = false;
            tempCrd.active[3]  = false;
            tempCrd.active[4]  = false;
            tempCrd.pos[0]     = x; // 当前段各轴终点位置
            tempCrd.pos[1]     = y;
            tempCrd.pos[2]     = 0;
            tempCrd.pos[3]     = 0;
            tempCrd.pos[4]     = 0;
            tempCrd.center[0]  = xCenter; // 圆弧插补的圆心坐标
            tempCrd.center[1]  = yCenter;
            tempCrd.center[2]  = 0;
            break;
        case API_CMD_ARC_YZ:
            tempCrd.circlePlat = CRD_ARC_YZ; // 圆弧插补的平面  0:xy 1:yz 2:zx
            tempCrd.active[0]  = false;      // 当前输入的哪几个轴数据
            tempCrd.active[1]  = TRUE;
            tempCrd.active[2]  = TRUE;
            tempCrd.active[3]  = false;
            tempCrd.active[4]  = false;
            tempCrd.pos[0]     = 0; // 当前段各轴终点位置
            tempCrd.pos[1]     = y;
            tempCrd.pos[2]     = z;
            tempCrd.pos[3]     = 0;
            tempCrd.pos[4]     = 0;
            tempCrd.center[0]  = 0; // 圆弧插补的圆心坐标
            tempCrd.center[1]  = yCenter;
            tempCrd.center[2]  = zCenter;
            break;
        case API_CMD_ARC_ZX:
            tempCrd.circlePlat = CRD_ARC_ZX; // 圆弧插补的平面  0:xy 1:yz 2:zx
            tempCrd.active[0]  = TRUE;       // 当前输入的哪几个轴数据
            tempCrd.active[1]  = false;
            tempCrd.active[2]  = TRUE;
            tempCrd.active[3]  = false;
            tempCrd.active[4]  = false;
            tempCrd.pos[0]     = x; // 当前段各轴终点位置
            tempCrd.pos[1]     = 0;
            tempCrd.pos[2]     = z;
            tempCrd.pos[3]     = 0;
            tempCrd.pos[4]     = 0;
            tempCrd.center[0]  = xCenter; // 圆弧插补的圆心坐标
            tempCrd.center[1]  = 0;
            tempCrd.center[2]  = zCenter;
            break;
        default:
            return CMD_API_ERROR_TYPE;
    }

    tempCrd.radius    = 0;         // 圆弧插补的半径
    tempCrd.circleDir = circleDir; // 圆弧旋转方向,0:顺时针;1:逆时针

    tempCrd.vel    = synVel; // 当前段合成目标速度
    tempCrd.acc    = synAcc; // 当前段合成加速度
    tempCrd.velEnd = velEnd; // 当前段合成终点速度

    rtn = CrdDataProcess(handle, crd, &tempCrd);
    if (CMD_SUCCESS != rtn)
    {
        return rtn;
    }
    return CMD_SUCCESS;
}

//前瞻初始化
short MC_InitLookAhead(TADMotionConn* handle, short crd, double accMax, TCrdData* pLookAheadBuf, short count)
{
    short crdIndex;
    TCardData* pCard = handle->CardData;
    crdIndex         = crd;

    pCard->gLookAheadPrm[crdIndex].enable = TRUE;
    pCard->gLookAheadPrm[crdIndex].crd    = crd;
    pCard->gLookAheadPrm[crdIndex].count  = count;
    pCard->gLookAheadPrm[crdIndex].pHostData = pLookAheadBuf; // pHostData 是向量 可以存储n个线段，其中pHostData是起始指针
    pCard->gLookAheadPrm[crdIndex].accMax = accMax;

    memset((void*)pLookAheadBuf, 0, sizeof(TCrdData) * count); //清零  把这个搞成结构数组  然后可以把所有的线段存进去
    //初始化前瞻控制结构体中参数
    InitLookAheadBuff(handle, crd);
    return CMD_SUCCESS;
}

#define CMP_DATA_LEN      9
#define SINGLE_SEND_POINT 10
short SendCmpPoint(
    TADMotionConn* handle, short crd, short LineNum, int nPntSize, double* tempP, int leftTimes, int pointnum, int curTimes)
{
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, CMD_2D_COMP_SEND_DATA);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, nPntSize);  //总点数
    Add16ToBuff(handle, tick, LineNum);   //总点数
    Add16ToBuff(handle, tick, pointnum);  //这一分包数据的点数
    Add16ToBuff(handle, tick, leftTimes); //剩余分包次数
    int i;
    int StartTime = 0;

    StartTime = curTimes * SINGLE_SEND_POINT;

    for (i = 0; i < pointnum; i++)
    {
        Add32ToBuff(handle, tick, *(tempP + i * 2));
        Add32ToBuff(handle, tick, *(tempP + i * 2 + 1));
    }
    short ret = SendCommand(handle, tick);
    CommandUninitial(handle, tick);
    return ret;
}

short MC_SetPosCmpPoint(TADMotionConn* handle, short crd, short LineNum, double pnt[10240][2], int nPntSize)
{

	double *pointAll = new double[nPntSize * 2];

    int    i,  rtn;
    for (i = 0; i < nPntSize; i++)
    {
        pointAll[i * 2]     = pnt[i][0];
        pointAll[i * 2 + 1] = pnt[i][1];
    }
    int timesPoint = 1;
    //计算传输次数
    if (nPntSize > SINGLE_SEND_POINT)
    {
        if ((nPntSize % SINGLE_SEND_POINT) != 0)
        {
            timesPoint = nPntSize / SINGLE_SEND_POINT + 1;
        }
        else
        {
            timesPoint = nPntSize / SINGLE_SEND_POINT;
        }
    }
    //分次发送
    double* tempP;
    int     pointnum;
    rtn = 0;
    for (int T = 0; T < timesPoint; T++)
    {
        tempP = &pointAll[SINGLE_SEND_POINT * T * 2];
        if (T == (timesPoint - 1))
        {
            pointnum = nPntSize % SINGLE_SEND_POINT;
            if (pointnum == 0)
                pointnum = SINGLE_SEND_POINT;
        }
        else
        {
            pointnum = SINGLE_SEND_POINT;
        }
        int leftTimes = timesPoint - T;
        int r         = SendCmpPoint(handle, crd, LineNum, nPntSize, tempP, leftTimes, pointnum, T);
        if (r != 0)
            rtn = r;
        std::this_thread::sleep_for(std::chrono::microseconds(2));
        delete [] pointAll;
    }
    return rtn;
}
// outtype :输出使能
short MC_SetPosCmpOutp(TADMotionConn* handle,
    short          crd,
    short            outNum,
    short            outtype,
    long         hlTime_ms,
    long         duty_ms)
{
    unsigned short tick = GetIndex(handle);
    if ((hlTime_ms < 0) && (duty_ms < 0) && (duty_ms < hlTime_ms))
    {
        return   CMD_API_ERROR_OUT_RANGE;
    }
    short hlTime_msH, hlTime_msL, duty_msH, duty_msL;

    hlTime_msH = (hlTime_ms >> 8) & 0xFF;
    hlTime_msL = (hlTime_ms     ) & 0xFF;
    duty_msH   = (duty_ms >> 8)   & 0xFF;
    duty_msL   = (duty_ms)        & 0xFF;

	CommandInitial(handle, tick, CMD_2D_COMP_OUTP);
	Add16ToBuff(handle, tick, crd);
	Add16ToBuff(handle, tick, outNum);
	Add16ToBuff(handle, tick, outtype);
	Add16ToBuff(handle, tick, hlTime_msL);
    Add16ToBuff(handle, tick, hlTime_msH);
    Add16ToBuff(handle, tick, duty_msL);
    Add16ToBuff(handle, tick, duty_msH);
	short ret = SendCommand(handle, tick);
	CommandUninitial(handle, tick);
	return ret;
}


short MC_PosCmpEnable(TADMotionConn* handle, short crd, bool bEnable,short error,short frontTime)
{
    unsigned short tick = GetIndex(handle);
    if ((error < 0) && (frontTime < 0))
    {
        return   CMD_API_ERROR_OUT_RANGE;
    }
    CommandInitial(handle, tick, CMD_2D_COMP_ENABLE);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, bEnable);
    Add16ToBuff(handle, tick, error);
    Add16ToBuff(handle, tick, frontTime);

    short ret = SendCommand(handle, tick);
    CommandUninitial(handle, tick);
    return ret;
}


short MC_ClearCmpPoint(TADMotionConn* handle, short crd)
{
    unsigned short tick = GetIndex(handle);
    if (crd < 0)
    {
        return CMD_API_ERROR_OUT_RANGE;
    }
    CommandInitial(handle, tick, CMD_2D_COMP_CLEAR);
    Add16ToBuff(handle, tick, crd);

    short ret = SendCommand(handle, tick);
    CommandUninitial(handle, tick);
    return ret;
}
