# ADMotion库接口函数清单

## 1. 卡相关指令（设备管理）

| 函数名 | 功能描述 | 参数说明 | 返回值 | 备注 |
|--------|----------|----------|--------|------|
| `API_CreateBoard()` | 创建运动控制句柄 | 无 | `TADMotionConn*` | 成功返回句柄，失败返回nullptr |
| `API_DeleteBoard(handle)` | 删除句柄，释放资源 | `handle`: 运动控制句柄 | `void` | 必须与CreateBoard配对使用 |
| `API_OpenBoard(handle, ip, port)` | 建立网络连接 | `handle`: 句柄<br/>`ip`: IP地址(***********/***********)<br/>`port`: 端口号(6666) | `short` | 错误码，1为成功 |
| `API_CloseBoard(handle)` | 关闭网络连接 | `handle`: 运动控制句柄 | `short` | 错误码，1为成功 |
| `API_ResetBoard(handle)` | 复位控制卡 | `handle`: 运动控制句柄 | `short` | 错误码，1为成功 |
| `API_SetLogCallback(handle, callback, userData)` | 设置日志回调函数 | `handle`: 句柄<br/>`callback`: 回调函数指针<br/>`userData`: 用户数据 | `short` | 支持自定义日志处理 |
| `API_SetLogLevel(handle, minLevel)` | 设置日志级别 | `handle`: 句柄<br/>`minLevel`: 最低日志级别 | `short` | LOG_DEBUG/INFO/WARNING/ERROR/FATAL |

## 2. 轴相关指令（轴控制）

| 函数名 | 功能描述 | 参数说明 | 返回值 | 备注 |
|--------|----------|----------|--------|------|
| `API_SetAxisPrm(handle, crd, AxisMap, AxisDir, VelMax, AccMax, Positive, Negative)` | 设置轴参数 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`AxisMap[2]`: 轴映射<br/>`AxisDir[2]`: 轴方向<br/>`VelMax[2]`: 最大速度<br/>`AccMax[2]`: 最大加速度<br/>`Positive[2]`: 正限位<br/>`Negative[2]`: 负限位 | `short` | 配置轴的基本参数 |
| `API_AxisOn(handle, axis)` | 轴使能 | `handle`: 句柄<br/>`axis`: 轴编号[0-3] | `short` | 使能指定轴 |
| `API_AxisOff(handle, axis)` | 轴禁用 | `handle`: 句柄<br/>`axis`: 轴编号[0-3] | `short` | 禁用指定轴 |
| `API_GoHome(handle, crd)` | 坐标系回零 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1] | `short` | 控制坐标系内所有轴回零 |
| `API_AxisGoHome(handle, axis)` | 单轴回零 | `handle`: 句柄<br/>`axis`: 轴编号[0-3] | `short` | 单个轴回零操作 |
| `API_AxisClearAlarm(handle, axis)` | 清除轴报警 | `handle`: 句柄<br/>`axis`: 轴编号[0-3] | `short` | 清除指定轴的报警状态 |
| `API_GetAixsPos(handle, axis, &pPos)` | 获取轴反馈位置 | `handle`: 句柄<br/>`axis`: 轴编号[0-3]<br/>`pPos`: 位置输出 | `short` | 获取编码器反馈位置 |
| `API_GetCmdAixsPos(handle, axis, &pPos)` | 获取轴指令位置 | `handle`: 句柄<br/>`axis`: 轴编号[0-3]<br/>`pPos`: 位置输出 | `short` | 获取指令位置 |
| `API_GetErrorCode(handle, axis, *ErrorCode)` | 获取轴错误码 | `handle`: 句柄<br/>`axis`: 轴编号[0-3]<br/>`ErrorCode`: 错误码输出 | `short` | 获取轴的错误状态 |
| `API_GetAxisStatus(handle, axis, &Sts)` | 获取轴状态 | `handle`: 句柄<br/>`axis`: 轴编号[0-3]<br/>`Sts`: 状态输出 | `short` | 获取轴的运行状态 |

## 3. JOG运动指令

| 函数名 | 功能描述 | 参数说明 | 返回值 | 备注 |
|--------|----------|----------|--------|------|
| `API_SetJogMode(handle, crd)` | 设置JOG模式 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1] | `short` | 切换到JOG运动模式 |
| `API_SetJogPrm(handle, crd, Maxvel, acc, dec, rate)` | 设置JOG参数 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`Maxvel`: 最大速度<br/>`acc`: 加速度<br/>`dec`: 减速度<br/>`rate`: 倍率(0,100] | `short` | 配置JOG运动参数 |
| `API_JogUpdate(handle, axis, dir)` | 启动JOG运动 | `handle`: 句柄<br/>`axis`: 轴编号[0-3]<br/>`dir`: 方向[-1,0,1] | `short` | -1负向，0停止，1正向 |

## 4. 坐标系点位运动

| 函数名 | 功能描述 | 参数说明 | 返回值 | 备注 |
|--------|----------|----------|--------|------|
| `API_SetCrdTrapMode(handle, crd)` | 设置坐标系点位模式 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1] | `short` | 切换到点位运动模式 |
| `API_SetCrdTrapPrm(handle, crd, posTarget, velMax, acc, rat)` | 设置坐标系点位参数 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`posTarget[2]`: 目标坐标<br/>`velMax`: 最大速度<br/>`acc`: 加速度<br/>`rat`: 倍率(0-100] | `short` | 配置点位运动参数 |
| `API_CrdTrapUpdate(handle, crd)` | 启动坐标系点位运动 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1] | `short` | 执行点位运动 |

## 5. 轴点位运动

| 函数名 | 功能描述 | 参数说明 | 返回值 | 备注 |
|--------|----------|----------|--------|------|
| `API_SetAxisTrapMode(handle, axis)` | 设置轴点位模式 | `handle`: 句柄<br/>`axis`: 轴编号[0-3] | `short` | 切换到轴点位模式 |
| `API_SetAxisTrapPrm(handle, axis, IncrPos, velMax, acc, rat)` | 设置轴点位参数 | `handle`: 句柄<br/>`axis`: 轴编号[0-3]<br/>`IncrPos`: 增量位置<br/>`velMax`: 最大速度<br/>`acc`: 加速度<br/>`rat`: 倍率(0-100) | `short` | 配置轴点位参数 |
| `API_AxisTrapUpdate(handle, crd)` | 启动轴点位运动 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1] | `short` | 执行轴点位运动 |

## 6. 坐标系插补指令

| 函数名 | 功能描述 | 参数说明 | 返回值 | 备注 |
|--------|----------|----------|--------|------|
| `API_SetCrdPrm(handle, crd, synVelMax, synAccMax)` | 设置坐标系插补参数 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`synVelMax`: 最大合成速度<br/>`synAccMax`: 最大合成加速度 | `short` | 配置插补参数 |
| `API_CrdStart(handle, crd)` | 坐标插补开始 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1] | `short` | 启动插补运动 |
| `API_CrdStop(crd)` | 坐标插补停止 | `crd`: 坐标系编号[0,1] | `short` | 停止插补运动 |
| `API_CrdPause(crd)` | 坐标插补暂停 | `crd`: 坐标系编号[0,1] | `short` | 暂停插补运动 |
| `API_GetCrdPos(handle, crd, *pPos)` | 获取坐标系反馈位置 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`pPos[2]`: 位置输出 | `short` | 获取坐标系反馈位置 |
| `API_GetCmdCrdPos(handle, crd, *pPos)` | 获取坐标系指令位置 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`pPos[2]`: 位置输出 | `short` | 获取坐标系指令位置 |

## 7. 直线圆弧指令

| 函数名 | 功能描述 | 参数说明 | 返回值 | 备注 |
|--------|----------|----------|--------|------|
| `API_Ln(handle, crd, x, y, synVel, synAcc)` | 直线插补 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`x,y`: 目标点坐标<br/>`synVel`: 同步速度<br/>`synAcc`: 同步加速度 | `short` | 执行直线插补 |
| `API_ArcXY_3point(handle, crd, p1, p2, p3, synVel, synAcc)` | 三点圆弧插补 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`p1[2],p2[2],p3[2]`: 三点坐标<br/>`synVel`: 同步速度<br/>`synAcc`: 同步加速度 | `short` | 通过三点定义圆弧 |
| `API_ArcXYR(handle, crd, x, y, radius, circleDir, synVel, synAcc)` | 半径圆弧插补 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`x,y`: 目标点坐标<br/>`radius`: 圆弧半径<br/>`circleDir`: 方向(0顺时针,1逆时针)<br/>`synVel`: 同步速度<br/>`synAcc`: 同步加速度 | `short` | 通过半径定义圆弧 |
| `API_ArcXYC(handle, crd, x, y, xCenter, yCenter, circleDir, synVel, synAcc)` | 中心圆弧插补 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`x,y`: 目标点坐标<br/>`xCenter,yCenter`: 圆心坐标<br/>`circleDir`: 方向(0顺时针,1逆时针)<br/>`synVel`: 同步速度<br/>`synAcc`: 同步加速度 | `short` | 通过圆心定义圆弧 |

## 8. 前瞻控制

| 函数名 | 功能描述 | 参数说明 | 返回值 | 备注 |
|--------|----------|----------|--------|------|
| `API_InitLookAhead(handle, crd, accMax, count)` | 前瞻初始化 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`accMax`: 系统最大加速度<br/>`count`: 前瞻段数 | `short` | 初始化前瞻功能 |
| `API_CloseLookAhead(handle, crd)` | 关闭前瞻 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1] | `short` | 关闭前瞻功能 |

## 9. 普通IO控制

| 函数名 | 功能描述 | 参数说明 | 返回值 | 备注 |
|--------|----------|----------|--------|------|
| `API_SetDeviceOutput(handle, *deviceOutput)` | 设置设备输出IO | `handle`: 句柄<br/>`deviceOutput`: IO输出值 | `short` | 控制普通IO输出 |
| `API_GetDeviceOutput(handle, *deviceOutput)` | 获取设备输出IO | `handle`: 句柄<br/>`deviceOutput`: IO输出值 | `short` | 读取普通IO输出状态 |
| `API_GetDeviceInput(handle, *deviceInput)` | 获取设备输入IO | `handle`: 句柄<br/>`deviceInput`: IO输入值 | `short` | 读取普通IO输入状态 |

## 10. 高速IO控制

| 函数名 | 功能描述 | 参数说明 | 返回值 | 备注 |
|--------|----------|----------|--------|------|
| `API_SetSpeedIOParam(handle, io_num, duty, period)` | 设置高速IO参数 | `handle`: 句柄<br/>`io_num`: IO编号[0,1]<br/>`duty`: 占空比[0-100]<br/>`period`: 周期[0-1000] | `short` | 配置PWM参数 |
| `API_SetSpeedIOState(handle, io_num, switch_state)` | 设置高速IO状态 | `handle`: 句柄<br/>`io_num`: IO编号[0,1]<br/>`switch_state`: 状态[0,1] | `short` | 控制高速IO开关 |
| `API_SetIOPluseEnable(handle, crd, io_num, IO_Enable)` | 设置IO脉冲使能 | `handle`: 句柄<br/>`crd`: 坐标系编号<br/>`io_num`: IO编号[0,1]<br/>`IO_Enable`: 使能状态 | `short` | 需FPGA版本>=260 |
| `API_SetIOPluseState(handle, crd, io_num, IO_State)` | 设置IO脉冲状态 | `handle`: 句柄<br/>`crd`: 坐标系编号<br/>`io_num`: IO编号[0,1]<br/>`IO_State`: 电平状态 | `short` | 直接输出高低电平 |
| `API_SetIOPluseTrigger(handle, crd, io_num, IO_Trigger)` | 触发IO脉冲翻转 | `handle`: 句柄<br/>`crd`: 坐标系编号<br/>`io_num`: IO编号[0,1]<br/>`IO_Trigger`: 触发信号 | `short` | 直接翻转电平 |
| `API_GetFpgaVersion(handle, &version)` | 获取FPGA版本 | `handle`: 句柄<br/>`version`: 版本号输出 | `short` | 检查FPGA版本兼容性 |

## 11. 位置比较输出

| 函数名 | 功能描述 | 参数说明 | 返回值 | 备注 |
|--------|----------|----------|--------|------|
| `API_PosCmpEnable(handle, crd, bEnable, error, frontTime)` | 位置比较使能 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`bEnable`: 使能标志<br/>`error`: 误差范围<br/>`frontTime`: 提前时间 | `short` | 开启/关闭位置比较功能 |
| `API_SetPosCmpPoint(handle, crd, seg, pnt[10240][2], nPntSize)` | 设置位置比较点 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`seg`: 段号<br/>`pnt`: 比较点数组<br/>`nPntSize`: 点数量 | `short` | 传输位置比较点位 |
| `API_ClearCmpPoint(handle, crd)` | 清除比较点 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1] | `short` | 清理所有比较点位 |
| `API_SetPosCmpOutp(handle, crd, outNum, outtype, hlTime_ms, duty_ms)` | 设置位置比较输出 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`outNum`: 输出编号<br/>`outtype`: 输出类型<br/>`hlTime_ms`: 高电平时间<br/>`duty_ms`: 占空比时间 | `short` | 配置比较输出参数 |

## 12. 速度IO点控制

| 函数名 | 功能描述 | 参数说明 | 返回值 | 备注 |
|--------|----------|----------|--------|------|
| `API_SendSpeedIO_Point(handle, crd, pointNum, p[])` | 发送速度IO点 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`pointNum`: 点数量<br/>`p[]`: SpeedIOPoint数组 | `short` | 传输速度IO点数据 |
| `API_SpeedIO_Enable(handle, crd, enable, IO_num)` | 速度IO使能 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1]<br/>`enable`: 使能标志<br/>`IO_num`: IO编号 | `short` | 开启/关闭速度IO功能 |
| `API_SpeedIO_ClearPoint(handle, crd)` | 清除速度IO点 | `handle`: 句柄<br/>`crd`: 坐标系编号[0,1] | `short` | 清理所有速度IO点 |

## 13. 调试功能

| 函数名 | 功能描述 | 参数说明 | 返回值 | 备注 |
|--------|----------|----------|--------|------|
| `API_DebugModelOption(handle, enable)` | 调试模式选项 | `handle`: 句柄<br/>`enable`: 调试模式开关 | `short` | 开启/关闭调试模式 |

## 单位说明

- **位置单位**: pulse (脉冲)
- **速度单位**: pulse/ms
- **加速度单位**: pulse/ms²
- **时间单位**: ms

### 单位转换示例
- 编码器分辨率: 1000 pulse = 1mm
- 速度转换: 1mm/s = 1 pulse/ms
- 加速度转换: 1mm/s² = 10⁻³ pulse/ms²

## 错误码说明

- **1**: CMD_SUCCESS - 操作成功
- **负数**: 各种错误类型
  - **-1**: 坐标系编号错误
  - **-2**: 轴号错误
  - **-7**: 轴正限位
  - **-8**: 轴负限位
  - **-13**: 轴报警
  - **-111**: 无效句柄

## 使用注意事项

1. **句柄管理**: 必须配对使用CreateBoard和DeleteBoard
2. **网络连接**: 支持IP地址***********和***********，端口6666
3. **轴编号映射**: axis=[0,1]对应crd=0，axis=[2,3]对应crd=1
4. **模式切换**: 不同运动模式需要先设置模式再设置参数
5. **线程安全**: 多线程环境下需要额外的同步机制
6. **错误处理**: 所有API函数都返回错误码，需要检查返回值
