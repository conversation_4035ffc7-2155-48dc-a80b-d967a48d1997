﻿/**
 * @file adconfig.h
 * @brief ADMotion运动控制库配置文件
 * @version 2.0 - 重构版本，使用统一错误码体系
 * @date 2024-12-19
 */

#pragma once
#ifndef ADCONFIG_H
#define ADCONFIG_H

// 包含统一的错误码定义
#include "motion_error_codes.h"

//=============================================================================
// 系统配置常量定义
//=============================================================================

#define MAX_LIMIT					12
#define MAX_ALARM					12
#define MAX_HOME					12
#define MAX_GPI						32
#define MAX_ARRIVE					12
#define MAX_MPG						1

#define MAX_ENABLE					12
#define MAX_CLEAR					12
#define MAX_GPO						32

#define MAX_PULSE					12
#define MAX_ENCODER					12
#define MAX_STEP					12
#define MAX_DAC						12
#define MAX_ADC						12

#define MAX_AXIS					8
#define MAX_PROFILE					2
#define MAX_CONTROL                 12

#define MAX_CRD_AXIS				2
#define MAX_CRD						2
#define MAX_CRD_FIFO_COUNT			2
#define MIN_BEZIER_TRANS_PIONT		10//定义贝塞尔过渡时，输入的L参数最少应该能生成10个贝塞尔插补点。

#define MAX_FRAM_BYTE				0x20000

//=============================================================================
// 插补指令类型定义
//=============================================================================
#define  API_CMD_LN_XY						0
#define  API_CMD_LN_XYZ						1
#define  API_CMD_LN_XYZA					2
#define  API_CMD_LN_XYZAB					3

#define  API_CMD_ARC_XY						10
#define  API_CMD_ARC_YZ						11
#define  API_CMD_ARC_ZX						12

#define  API_CMD_TRANS_TYPE_L				20

#define MAX_COMPARE_BUFFER					1024

//=============================================================================
// 兼容性函数声明
//=============================================================================
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 获取错误码对应的错误描述信息（兼容性函数）
 * @param ErrCode 错误码
 * @return 错误描述字符串
 * @note 此函数保持向后兼容，内部调用新的GetMotionErrorString函数
 */
extern const wchar_t * GetErrorStr(int ErrCode);

#ifdef __cplusplus
}
#endif
#endif
