﻿#pragma once
#include "ADOSType.h"

void GetArcCenter(double* pStartPoint, double* pEndPoint, double* pArcCenter, short circleDir, double radius);

void GetVectorCross(double* u, double* v, double* a);

double GetVectorNorm(double* data);

double GetVectorDot(double* data1, double* data2);

void GetCirclePoint(double* pointB, double* tanVector, double* P);

double MinSpeed(double a, double b, double c);
double abs_z(double a);
short  CalArcRadius_dir(int32_t* p1, int32_t* p2, int32_t* p3, double* radius1, double* circleDir1);
short  CalArcCenter_dir(int32_t* p1, int32_t* p2, int32_t* p3, double* center, short* circleDir1);
void   VectorCross(int32_t* Vector01, int32_t* Vector02, int32_t* calVector);
short readData(long data1[50][2],short *length1);
