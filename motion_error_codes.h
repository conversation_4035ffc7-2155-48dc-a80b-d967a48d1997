/**
 * @file motion_error_codes.h
 * @brief ADMotion运动控制库统一错误码定义
 * @version 2.0
 * @date 2024-12-19
 * 
 * 本文件统一定义了ADMotion库中所有的错误码，按功能分类组织，
 * 使用连续的数值分配和清晰的命名规范。
 */

#pragma once
#ifndef MOTION_ERROR_CODES_H
#define MOTION_ERROR_CODES_H

//=============================================================================
// 错误码分类体系说明
//=============================================================================
/**
 * 错误码分类和数值范围分配：
 *
 * 0        : 成功状态
 * 20-99    : 参数和输入验证错误
 * 100-102  : 通信和连接错误
 * 45-51    : 坐标系和插补错误
 * 0x0101+  : 伺服驱动器错误
 *
 * 负数错误码保留用于DSP底层错误
 */

//=============================================================================
// 1. 成功状态 (0)
//=============================================================================
#define MOTION_SUCCESS                          0       // 操作成功

//=============================================================================
// 2. 通信和连接错误 (1-99) - 仅保留实际使用的错误码
//=============================================================================
#define MOTION_ERROR_COMM_TIMEOUT               100     // 通信超时（原TIMEOUT）
#define MOTION_ERROR_COMM_FORMAT                101     // 数据格式错误（原FORMATERROR）
#define MOTION_ERROR_COMM_CONNECTION            102     // 连接错误（原CONNECTIONERROR）

//=============================================================================
// 3. 参数和输入验证错误 (20-99) - 仅保留实际使用的错误码
//=============================================================================
#define MOTION_ERROR_PARAM_OUT_RANGE            20      // 参数超出有效范围
#define MOTION_ERROR_PARAM_TYPE                 21      // 参数类型错误
#define MOTION_ERROR_PARAM_POINTER              22      // 指针参数无效
#define MOTION_ERROR_PARAM_INVALID              23      // 参数值无效
#define MOTION_ERROR_PARAM_HANDLE_INVALID       -111    // 句柄无效（保持原值-111以确保兼容性）

//=============================================================================
// 4. 坐标系和插补错误 (40-59) - 仅保留实际使用的错误码
//=============================================================================
#define MOTION_ERROR_CRD_LINE_ZERO_LENGTH       45      // 插补直线长度为零
#define MOTION_ERROR_CRD_ARC_CENTER             47      // 圆弧圆心坐标错误
#define MOTION_ERROR_CRD_ARC_END_POSITION       48      // 圆弧终点位置错误
#define MOTION_ERROR_CRD_ARC_RADIUS             49      // 圆弧半径错误
#define MOTION_ERROR_CRD_ARC3D_COLLINEAR        50      // 空间圆弧三点共线
#define MOTION_ERROR_CRD_ARC3D_RADIUS_SMALL     51      // 空间圆弧半径过小



//=============================================================================
// 5. DSP底层错误 (负数区间)
//=============================================================================
#define MOTION_ERROR_DSP_LOAD                   (-5)        // DSP数据加载错误
#define MOTION_ERROR_DSP_PARSE                  (-6)        // DSP解析错误
#define MOTION_ERROR_DSP_ADD_RESULT             (-7)        // DSP添加结果错误
#define MOTION_ERROR_DSP_PARAMETER              (-10)       // DSP参数错误
#define MOTION_ERROR_DSP_AXIS_NOT_ENABLED       (-11)       // DSP轴未使能
#define MOTION_ERROR_DSP_AXIS_ENABLED           (-12)       // DSP轴已使能
#define MOTION_ERROR_DSP_AXIS_RUNNING           (-13)       // DSP轴正在运动
#define MOTION_ERROR_DSP_PROFILE_RUNNING        (-14)       // DSP规划正在运动
#define MOTION_ERROR_DSP_AXIS_MAP               (-15)       // DSP轴映射错误
#define MOTION_ERROR_DSP_AXIS_STATUS            (-16)       // DSP轴状态异常
#define MOTION_ERROR_DSP_PROFILE_MODE           (-17)       // DSP规划模式错误
#define MOTION_ERROR_DSP_HOOK                   (-18)       // DSP有挂接
#define MOTION_ERROR_DSP_PROFILE_MODE_HOME      (-19)       // DSP规划处于回零模式
#define MOTION_ERROR_DSP_UNKNOWN                (-20)       // DSP未知指令
#define MOTION_ERROR_DSP_CRD_HOST_FIFO_RUN      (-30)       // DSP插补主FIFO正在运行
#define MOTION_ERROR_DSP_CRD_FIFO1_RUN          (-31)       // DSP插补FIFO1正在运行
#define MOTION_ERROR_DSP_CRD_AXIS_MAP_SAME      (-32)       // DSP多个轴映射相同
#define MOTION_ERROR_DSP_CRD_FIFO_OVERFLOW      (-33)       // DSP缓存区溢出
#define MOTION_ERROR_DSP_HOME_LIMIT_MAP         (-50)       // DSP回零限位映射异常
#define MOTION_ERROR_DSP_COMPARE_NOT_CONFIG     (-70)       // DSP位置比较未配置
#define MOTION_ERROR_DSP_COMPARE_RUNNING        (-71)       // DSP位置比较正在运行
#define MOTION_ERROR_DSP_AUTO_TRIG_CRD_NO_CFG   (-90)       // DSP自动触发坐标系未配置

//=============================================================================
// 6. 伺服驱动器错误码 (0x0100-0xEA40) - 根据实际伺服驱动器错误码更新
//=============================================================================

// 系统参数类错误 (0x0101-0x0136)
#define MOTION_ERROR_SERVO_SYS_PARAM_0101       0x0101      // 系统参数异常
#define MOTION_ERROR_SERVO_SYS_PARAM_0222       0x0222      // 系统参数异常
#define MOTION_ERROR_SERVO_SYS_PARAM_0333       0x0333      // 系统参数异常
#define MOTION_ERROR_SERVO_LOGIC_CONFIG         0x0102      // 逻辑配置故障
#define MOTION_ERROR_SERVO_FPGA_MCU_MISMATCH    0x0103      // FPGA和MCU产品型号不匹配
#define MOTION_ERROR_SERVO_FPGA_INTERRUPT       0x0104      // FPGA中断发送故障 运行超时
#define MOTION_ERROR_SERVO_SYS_PARAM_RESET      0x0105      // 系统参数异常 不可复位,需恢复出厂参数
#define MOTION_ERROR_SERVO_PARAM_STORAGE        0x0108      // 参数存储故障
#define MOTION_ERROR_SERVO_FACTORY_PARAM        0x0111      // 厂家参数异常
#define MOTION_ERROR_SERVO_ENCODER_MISMATCH     0x0120      // 产品匹配故障,无对应的编码器
#define MOTION_ERROR_SERVO_MOTOR_PARAM_MISMATCH 0x0122      // 设置绝对位置功能时电机参数不匹配
#define MOTION_ERROR_SERVO_MOTOR_ROM_DATA       0x0136      // 电机ROM中数据校验错误或未存入参数

// 过流和电流类错误 (0x0200-0x0234, 0x2207)
#define MOTION_ERROR_SERVO_SOFTWARE_OVERCURRENT 0x0200      // 软件过流故障
#define MOTION_ERROR_SERVO_HARDWARE_OVERCURRENT 0x0201      // 硬件过流故障
#define MOTION_ERROR_SERVO_MCU_TORQUE_TIMEOUT   0x0208      // MCU未及时更新转矩指令引起的超时错误
#define MOTION_ERROR_SERVO_OUTPUT_SHORT_GND     0x0210      // 输出对地短路
#define MOTION_ERROR_SERVO_UVW_WIRE_ERROR       0x0234      // UVW三相接错线后的飞车报警
#define MOTION_ERROR_SERVO_DQ_CURRENT_OVERFLOW  0x2207      // D/Q电流溢出

// 电压类错误 (0x0430, 0x2400, 0x2410, 0x6430)
#define MOTION_ERROR_SERVO_CTRL_POWER_UNDER     0x0430      // 控制电源欠电压
#define MOTION_ERROR_SERVO_OVERVOLTAGE          0x2400      // 过电压
#define MOTION_ERROR_SERVO_UNDERVOLTAGE         0x2410      // 欠电压

#define MOTION_ERROR_SERVO_CTRL_POWER_UNDER2    0x6430      // 控制电源欠电压

// AD采样类错误 (0x0834, 0x0835, 0x6834, 0x6835)
#define MOTION_ERROR_SERVO_DSP_AD_OVERVOLT      0x0834      // DSP的AD采样过压故障
#define MOTION_ERROR_SERVO_FPGA_AD_FAULT        0x0835      // FPGA报出的AD采样故障
#define MOTION_ERROR_SERVO_DSP_AD_OVERVOLT2     0x6834      // DSP的AD采样过压故障
#define MOTION_ERROR_SERVO_FPGA_AD_FAULT2       0x6835      // FPGA报出的AD采样故障

// 编码器类错误 (0x0740, 0x0A33-0x0A35, 0x6731, 0x6733, 0x6735, 0x6745, 0x2770)
#define MOTION_ERROR_SERVO_ENCODER_Z_NOISE      0x0740      // 编码器Z干扰故障
#define MOTION_ERROR_SERVO_ENCODER_PARAM_ERR    0x0A33      // 编码器参数异常
#define MOTION_ERROR_SERVO_ENCODER_CHECKSUM_ERR 0x0A34      // 编码器回送校验异常
#define MOTION_ERROR_SERVO_ENCODER_Z_BREAK      0x0A35      // Z断线
#define MOTION_ERROR_SERVO_ENCODER_BATTERY      0x6731      // 编码器电池失效
#define MOTION_ERROR_SERVO_ENCODER_MULTI_COUNT  0x6733      // 编码器多圈计数错误
#define MOTION_ERROR_SERVO_ENCODER_MULTI_OVER   0x6735      // 编码器多圈计数器溢出
#define MOTION_ERROR_SERVO_ENCODER_Z_NOISE2     0x6745      // 编码器Z干扰故障
#define MOTION_ERROR_SERVO_EXT_ENCODER_SCALE    0x2770      // 外部编码器标尺故障

// 功能分配类错误 (0x2130, 0x2131)
#define MOTION_ERROR_SERVO_DI_FUNC_ASSIGN       0x2130      // DI功能分配故障(除了重复分配故障外，还包括分配功能超限，手轮中断定长分配功能不合理等故障)
#define MOTION_ERROR_SERVO_DO_FUNC_ASSIGN       0x2131      // DO功能分配故障

// 速度和运动类错误 (0x2500, 0x6500)
#define MOTION_ERROR_SERVO_SPEED_OVER_MAX       0x2500      // 速度超过最高转速
#define MOTION_ERROR_SERVO_SPEED_OVER_MAX2      0x6500      // 速度超过最高转速

// 辨识类错误 (0x2600, 0x2602, 0xEA40)
#define MOTION_ERROR_SERVO_INERTIA_ID_FAIL      0x2600      // 离线惯量辨识失败
#define MOTION_ERROR_SERVO_ANGLE_ID_FAIL        0x2602      // 角度辨识失败
#define MOTION_ERROR_SERVO_PARAM_ID_FAIL        0xEA40      // 参数辨识失败

// 伺服控制类错误 (0x6121, 0x6300)
#define MOTION_ERROR_SERVO_ON_CMD_INVALID       0x6121      // 伺服ON指令无效故障
#define MOTION_ERROR_SERVO_STO_SIGNAL_PROTECT   0x6300      // STO信号输入保护

// 电源和继电器类错误 (0x6420, 0x6421)
#define MOTION_ERROR_SERVO_POWER_LINE_MISSING   0x6420      // 电源线缺相
#define MOTION_ERROR_SERVO_RELAY_FAULT          0x6421      // 继电器故障

// 过载和温度类错误 (0x6610, 0x6620, 0x6630, 0x6650, 0x6660, 0xE909)
#define MOTION_ERROR_SERVO_DRIVER_OVERLOAD      0x6610      // 驱动器过载
#define MOTION_ERROR_SERVO_MOTOR_OVERLOAD       0x6620      // 电机过载
#define MOTION_ERROR_SERVO_MOTOR_STALL_HEAT     0x6630      // 电机堵转过热保护
#define MOTION_ERROR_SERVO_HEATSINK_OVERHEAT    0x6650      // 散热片温度过高(来自电流累加信号)
#define MOTION_ERROR_SERVO_MOTOR_OVERHEAT       0x6660      // 电机温度过高
#define MOTION_ERROR_SERVO_MOTOR_OVERLOAD_WARN  0xE909      // 电机过载警告

// 脉冲输出类错误 (0x6510, 0xE110)
#define MOTION_ERROR_SERVO_PULSE_OUT_OVERSPEED  0x6510      // 分频脉冲输出过速
#define MOTION_ERROR_SERVO_PULSE_OUT_SET_ERR    0xE110      // 分频脉冲输出设定故障

// 抱闸类错误 (0x6625, 0x6626)
#define MOTION_ERROR_SERVO_BRAKE_ABNORMAL_CLOSE 0x6625      // 抱闸非正常关闭
#define MOTION_ERROR_SERVO_BRAKE_ABNORMAL_OPEN  0x6626      // 抱闸非正常打开

// 位置类错误 (0x6B00-0x6B06)
#define MOTION_ERROR_SERVO_POSITION_ERROR       0x6B00      // 位置偏差过大
#define MOTION_ERROR_SERVO_POSITION_CMD_ERR     0x6B01      // 位置指令输入异常
#define MOTION_ERROR_SERVO_FULL_CLOSE_POS_ERR   0x6B02      // 全闭环位置偏差过大
#define MOTION_ERROR_SERVO_ELEC_GEAR_SET_ERR    0x6B03      // 电子齿轮设定错误
#define MOTION_ERROR_SERVO_FULL_CLOSE_SWITCH    0x6B04      // 全闭环和多段位置绝对模式混用时不能内外切换
#define MOTION_ERROR_SERVO_GANTRY_SYNC_POS_ERR  0x6B05      // 龙门同步位置偏差过大
#define MOTION_ERROR_SERVO_GANTRY_SYNC_TOR_ERR  0x6B06      // 龙门同步转矩偏差过大

// 制动电阻类错误 (0xE920-0xE922)
#define MOTION_ERROR_SERVO_REGEN_RESIST_OVERLOAD 0xE920     // 再生制动电阻过载
#define MOTION_ERROR_SERVO_REGEN_RESIST_OVERCUR  0xE921     // 再生制动电阻过流
#define MOTION_ERROR_SERVO_EXT_REGEN_RESIST_SMALL 0xE922    // 外接再生制动电阻阻值过小

// 回零类错误 (0xE601)
#define MOTION_ERROR_SERVO_HOME_RETURN_TIMEOUT  0xE601      // 原点复归回零超时错误

// 其他故障类错误 (0xE831, 0xE939)
#define MOTION_ERROR_SERVO_AI_ZERO_OFFSET_LARGE 0xE831      // AI零偏过大
#define MOTION_ERROR_SERVO_MOTOR_POWER_BREAK    0xE939      // 电机动力线断线

// 警告类错误 (0xE900, 0xE941-0xE942, 0xE950, 0xE952, 0xE980, 0xE990, 0xE994-0xE998)
#define MOTION_ERROR_SERVO_EMERGENCY_STOP_WARN  0xE900      // 紧急停机警告
#define MOTION_ERROR_SERVO_PARAM_CHANGE_REBOOT  0xE941      // 需要重新接通电源的参数变更
#define MOTION_ERROR_SERVO_PARAM_STORE_FREQ     0xE942      // 参数存储频繁警告
#define MOTION_ERROR_SERVO_POSITIVE_LIMIT_WARN  0xE950      // 正向超程警告
#define MOTION_ERROR_SERVO_NEGATIVE_LIMIT_WARN  0xE952      // 负向超程警告
#define MOTION_ERROR_SERVO_ENCODER_ALG_WARN     0xE980      // 编码器算法异常警告
#define MOTION_ERROR_SERVO_POWER_PHASE_WARN     0xE990      // 电源缺相警告
#define MOTION_ERROR_SERVO_CANLINK_ADDR_CONFLICT 0xE994     // CANLINK地址冲突
#define MOTION_ERROR_SERVO_BUS_RECOVER_WARN     0xE995      // 总线恢复警告
#define MOTION_ERROR_SERVO_BUS_PASSIVE_ERR_WARN 0xE996      // 总线被动错误警告
#define MOTION_ERROR_SERVO_POS_CMD_FEEDBACK_LIMIT 0xE997    // 位置指令或位置反馈达到极限值
#define MOTION_ERROR_SERVO_OBJ_DICT_VALUE_ERR   0xE998      // 对象字典数值设置错误

//=============================================================================
// 兼容性定义 - 保持与旧版本的兼容性（仅保留实际使用的错误码）
//=============================================================================
// 旧版本错误码映射到新版本
#define CMD_SUCCESS                             MOTION_SUCCESS
#define CMD_API_ERROR_OUT_RANGE                 MOTION_ERROR_PARAM_OUT_RANGE
#define CMD_API_ERROR_INVALID_HANDLE            MOTION_ERROR_PARAM_HANDLE_INVALID
#define CMD_API_ERROR_TYPE                      MOTION_ERROR_PARAM_TYPE
#define CMD_API_ERROR_POINTER                   MOTION_ERROR_PARAM_POINTER
#define CMD_API_ERROR_PRM                       MOTION_ERROR_PARAM_INVALID

// 通信相关兼容性映射（实际使用的）
#define CMD_API_ERROR_OPEN                      1       // 打开连接失败（ioctrl.cpp中使用）
#define TIMEOUT                                 MOTION_ERROR_COMM_TIMEOUT
#define FORMATERROR                             MOTION_ERROR_COMM_FORMAT
#define CONNECTIONERROR                         MOTION_ERROR_COMM_CONNECTION

// 坐标系相关兼容性映射（实际使用的）
#define CMD_API_ERROR_CRD_FIFO_FULL             40      // FIFO缓冲区满（lookahead.cpp中使用）
#define CMD_API_ERROR_CRD_LINE_ZERO             MOTION_ERROR_CRD_LINE_ZERO_LENGTH
#define CMD_API_ERROR_CRD_ARC_CENTER            MOTION_ERROR_CRD_ARC_CENTER
#define CMD_API_ERROR_CRD_ARC_END_POS           MOTION_ERROR_CRD_ARC_END_POSITION
#define CMD_API_ERROR_CRD_ARC_RADIUS            MOTION_ERROR_CRD_ARC_RADIUS
#define CMD_API_ERROR_CRD_ARC3D_COLLINEAR       MOTION_ERROR_CRD_ARC3D_COLLINEAR
#define CMD_API_ERROR_CRD_ARC3D_RADIUS_SMALL    MOTION_ERROR_CRD_ARC3D_RADIUS_SMALL

// 导出接口兼容性（保留admc_export.cpp中实际使用的）
#define ADMC_SUCCESS                            1  // 保持原值以确保兼容性
#define ADMC_ERROR_OPEN                         100
#define ADMC_ERROR_OUT_RANGE                    101
#define ADMC_ERROR_TYPE                         102
#define ADMC_ERROR_CRD_DEMESION                 103
#define ADMC_ERROR_CRD_DATA                     104
#define ADMC_ERROR_CRD_MODE                     105
#define ADMC_ERROR_CRD_RUN                      106
#define ADMC_ERROR_CRD_STOP                     107
#define ADMC_ERROR_CRD_PAUSE                    108
#define ADMC_ERROR_CRD_DATA2                    109
#define ADMC_ERROR_CRD_BUF_FULL                 110
#define ADMC_ERROR_CRD_BUF_EMPTY                111
#define ADMC_ERROR_CRD_BUF_OVERFLOW             112
#define ADMC_ERROR_CRD_BUF_UNDERFLOW            113
#define ADMC_ERROR_CRD_BUF_DATA                 114
#define ADMC_ERROR_CRD_BUF_DATA_TYPE            115
#define ADMC_ERROR_CRD_BUF_DATA_SUB_TYPE        116

//=============================================================================
// 错误信息函数声明
//=============================================================================
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 获取错误码对应的中文描述信息
 * @param errorCode 错误码
 * @return 错误描述字符串
 */
const wchar_t* GetMotionErrorString(int errorCode);

/**
 * @brief 获取错误码对应的英文描述信息
 * @param errorCode 错误码
 * @return 错误描述字符串
 */
const char* GetMotionErrorStringA(int errorCode);

#ifdef __cplusplus
}
#endif

#endif // MOTION_ERROR_CODES_H
