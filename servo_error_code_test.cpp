/**
 * @file servo_error_code_test.cpp
 * @brief 伺服错误码测试程序
 * 
 * 测试所有新添加的伺服错误码是否能正确显示中文描述
 */

#include <iostream>
#include <iomanip>
#include <vector>
#include "motion_error_codes.h"

// 将宽字符串转换为UTF-8字符串（用于控制台输出）
std::string wstring_to_utf8(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    
    // 简单的ASCII转换（仅用于测试）
    std::string result;
    for (wchar_t wc : wstr) {
        if (wc < 128) {
            result += static_cast<char>(wc);
        } else {
            result += "?"; // 非ASCII字符用?替代
        }
    }
    return result;
}

int main() {
    std::cout << "=== 伺服错误码测试程序 ===" << std::endl;
    
    // 定义要测试的错误码列表
    std::vector<int> testErrorCodes = {
        // 系统参数类错误
        0x0101, 0x0222, 0x0333, 0x0102, 0x0103, 0x0104, 0x0105, 0x0108, 0x0111, 0x0120, 0x0122, 0x0136,
        
        // 过流和电流类错误
        0x0200, 0x0201, 0x0208, 0x0210, 0x0234, 0x2207,
        
        // 电压类错误
        0x0430, 0x2400, 0x2410, 0x6430,
        
        // AD采样类错误
        0x0834, 0x0835, 0x6834, 0x6835,
        
        // 编码器类错误
        0x0740, 0x0A33, 0x0A34, 0x0A35, 0x6731, 0x6733, 0x6735, 0x6745, 0x2770,
        
        // 功能分配类错误
        0x2130, 0x2131,
        
        // 速度和运动类错误
        0x2500, 0x6500,
        
        // 辨识类错误
        0x2600, 0x2602, 0xEA40,
        
        // 伺服控制类错误
        0x6121, 0x6300,
        
        // 电源和继电器类错误
        0x6420, 0x6421,
        
        // 过载和温度类错误
        0x6610, 0x6620, 0x6630, 0x6650, 0x6660, 0xE909,
        
        // 脉冲输出类错误
        0x6510, 0xE110,
        
        // 抱闸类错误
        0x6625, 0x6626,
        
        // 位置类错误
        0x6B00, 0x6B01, 0x6B02, 0x6B03, 0x6B04, 0x6B05, 0x6B06,
        
        // 制动电阻类错误
        0xE920, 0xE921, 0xE922,
        
        // 回零类错误
        0xE601,
        
        // 其他故障类错误
        0xE831, 0xE939,
        
        // 警告类错误
        0xE900, 0xE941, 0xE942, 0xE950, 0xE952, 0xE980, 0xE990, 0xE994, 0xE995, 0xE996, 0xE997, 0xE998
    };
    
    std::cout << "\n测试 " << testErrorCodes.size() << " 个伺服错误码..." << std::endl;
    std::cout << "格式: 错误码(十六进制) | 错误码(十进制) | 错误描述" << std::endl;
    std::cout << std::string(80, '-') << std::endl;
    
    int successCount = 0;
    int failCount = 0;
    
    for (int errorCode : testErrorCodes) {
        const wchar_t* errorDesc = GetMotionErrorString(errorCode);
        std::wstring errorWStr(errorDesc);
        std::string errorStr = wstring_to_utf8(errorWStr);
        
        // 检查是否获取到有效的错误描述
        bool hasValidDesc = !errorWStr.empty() && errorWStr != L"未知错误";
        
        std::cout << "0x" << std::hex << std::uppercase << std::setw(4) << std::setfill('0') << errorCode
                  << " | " << std::dec << std::setw(5) << errorCode
                  << " | " << errorStr;
        
        if (hasValidDesc) {
            std::cout << " ✓" << std::endl;
            successCount++;
        } else {
            std::cout << " ✗ (未找到描述)" << std::endl;
            failCount++;
        }
    }
    
    std::cout << std::string(80, '-') << std::endl;
    std::cout << "测试结果: 成功 " << successCount << " 个, 失败 " << failCount << " 个" << std::endl;
    
    // 特别测试0xE998错误码（用户提到的问题错误码）
    std::cout << "\n=== 特别测试 0xE998 错误码 ===" << std::endl;
    int testCode = 0xE998;
    const wchar_t* testDesc = GetMotionErrorString(testCode);
    std::cout << "错误码: 0x" << std::hex << std::uppercase << testCode << " (" << std::dec << testCode << ")" << std::endl;
    std::cout << "描述: " << wstring_to_utf8(std::wstring(testDesc)) << std::endl;
    
    // 测试数据类型转换
    unsigned short unsignedCode = static_cast<unsigned short>(testCode);
    short signedCode = static_cast<short>(testCode);
    std::cout << "作为 unsigned short: " << unsignedCode << std::endl;
    std::cout << "作为 short: " << signedCode << std::endl;
    
    if (failCount == 0) {
        std::cout << "\n🎉 所有伺服错误码测试通过！" << std::endl;
    } else {
        std::cout << "\n⚠️  有 " << failCount << " 个错误码未找到描述，请检查错误码定义。" << std::endl;
    }
    
    return 0;
}
