/**
 * @file error_code_display_test.cpp
 * @brief 错误码显示测试程序
 * 
 * 测试伺服错误码（如0xE998）在日志系统中的正确显示
 */

#include <iostream>
#include <iomanip>
#include "motion_types.h"
#include "admc_pci.h"
#include "logger_proxy.h"

// 测试日志回调函数
void TestLogCallback(LogLevel level, const char* message, void* userData) {
    const char* levelStr = "";
    switch (level) {
        case LOG_ERROR:   levelStr = "ERROR"; break;
        case LOG_WARNING: levelStr = "WARN";  break;
        case LOG_INFO:    levelStr = "INFO";  break;
        case LOG_DEBUG:   levelStr = "DEBUG"; break;
        default:          levelStr = "UNKNOWN"; break;
    }
    
    std::cout << "[" << levelStr << "] " << message << std::endl;
}

int main() {
    std::cout << "=== 错误码显示测试程序 ===" << std::endl;
    
    // 1. 测试数据类型转换
    std::cout << "\n1. 数据类型转换测试:" << std::endl;
    
    unsigned short originalCode = 0xE998;  // 59800
    short signedCode = static_cast<short>(originalCode);
    int intCode = static_cast<int>(originalCode);
    
    std::cout << "原始错误码 (unsigned short): 0x" << std::hex << std::uppercase 
              << originalCode << " (" << std::dec << originalCode << ")" << std::endl;
    std::cout << "转换为 short: " << signedCode << std::endl;
    std::cout << "转换为 int: " << intCode << std::endl;
    
    // 2. 测试错误类型判断
    std::cout << "\n2. 错误类型判断测试:" << std::endl;
    
    int errorType1 = GetErrorCodeType(signedCode);  // 使用有符号值
    int errorType2 = GetErrorCodeType(intCode);     // 使用int值
    
    std::cout << "GetErrorCodeType(" << signedCode << ") = " << errorType1 << std::endl;
    std::cout << "GetErrorCodeType(" << intCode << ") = " << errorType2 << std::endl;
    
    // 3. 模拟API_GetErrorCode调用
    std::cout << "\n3. 模拟API调用测试:" << std::endl;
    
    // 创建模拟句柄（注意：这只是为了测试，实际使用需要真实的连接）
    TADMotionConn* testHandle = nullptr;
    
    // 模拟API_OpenBoard
    short openResult = API_OpenBoard(&testHandle, "192.168.2.2", 6666);
    if (openResult == CMD_SUCCESS && testHandle) {
        std::cout << "成功创建测试句柄" << std::endl;
        
        // 设置日志回调
        API_SetLogCallback(testHandle, TestLogCallback, nullptr);
        API_SetLogLevel(testHandle, LOG_DEBUG);
        
        // 测试错误码获取
        unsigned short errorCode = 0;
        std::cout << "\n调用 API_GetErrorCode..." << std::endl;
        short result = API_GetErrorCode(testHandle, 0, &errorCode);
        
        std::cout << "API_GetErrorCode 返回值: " << result << std::endl;
        std::cout << "获取的错误码: 0x" << std::hex << std::uppercase 
                  << errorCode << " (" << std::dec << errorCode << ")" << std::endl;
        
        // 关闭连接
        API_CloseBoard(testHandle);
    } else {
        std::cout << "无法创建测试句柄，跳过API测试" << std::endl;
        
        // 直接测试LogAndReturnError函数
        std::cout << "\n4. 直接测试LogAndReturnError函数:" << std::endl;
        
        // 使用nullptr句柄进行测试（只测试函数逻辑，不实际记录日志）
        short result1 = LogAndReturnError(nullptr, signedCode, "TestFunction", "测试有符号转换");
        short result2 = LogAndReturnError(nullptr, intCode, "TestFunction", "测试int值");
        
        std::cout << "LogAndReturnError(signedCode=" << signedCode << ") 返回: " << result1 << std::endl;
        std::cout << "LogAndReturnError(intCode=" << intCode << ") 返回: " << result2 << std::endl;
    }
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    return 0;
}
