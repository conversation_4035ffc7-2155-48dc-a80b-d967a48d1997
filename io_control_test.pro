QT -= gui
CONFIG += console
CONFIG -= app_bundle
CONFIG += c++14

# 包含ADMotion库的头文件目录
INCLUDEPATH += $$PWD \
               $$PWD/../ASIOPackage \
               $$PWD/../ \
               $$PWD/../ADSemaphore

# 引用ADMotion库
LIBS += -L$$PWD/../lib -lADMotion -lASIOPackage
LIBS += -lws2_32 -lpthread

# 源文件
SOURCES += io_control_test.cpp

# 默认规则
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

msvc{
QMAKE_CXXFLAGS += -utf-8
}
win32-msvc*{
QMAKE_CXXFLAGS_WARN_ON -= -w34100 # C4100 -Wno-unused-parameter
}else{
QMAKE_CXXFLAGS_WARN_ON +=  -Wno-unused-parameter \
                           -Wno-unused-but-set-variable\
                           -Wno-unused-variable
}
