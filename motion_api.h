#pragma once
#ifndef MOTION_API_H
#define MOTION_API_H

#include <memory>
#include <array>
#include <vector>
#include <string>
#include <map>
#include <functional>
#include <mutex>
#include "optional.h"
#include "motion_conn.h"
#include "motion_types.h"
#include "motion_enums.h"

/**
 * @brief 运动控制错误码枚举类
 * 将原来的错误码宏定义转换为枚举类型
 */
enum class MotionErrorCode : short {
    Success = 1,                    // 成功
    CrdNumError = -1,               // 坐标系编号错误
    AxisNumError = -2,              // 轴号错误
    AxisDirError = -3,              // 轴方向错误
    AxisEnableError = -4,           // 轴使能错误
    AxisPositiveLimError = -7,      // 轴正限位
    AxisNegativeLimError = -8,      // 轴负限位
    AxisRunError = -9,              // 关节轴运行错误
    PrfRunError = -10,              // 坐标轴运行错误
    MotionTypeError = -11,          // 插补运动类型错误
    CirclePlantError = -12,         // 圆弧规划错误
    AxisAlarm = -13,                // 轴报警
    OriginFlagError = -40,          // 原点报错
    LoadError = -15,                // 数据加载错误
    ParseError = -16,               // 暂停错误
    AddResultError = -17,           // 轨迹结束错误
    ParameterError = -18,           // 参数错误
    AxisDisenableError = -19,       // 轴未使能
    AxisEnableStateError = -20,     // 轴使能状态错误
    AxisMapError = -21,             // 轴表错误
    AxisStsError = -22,             // 轴状态错误
    PrfModeError = -23,             // 轨迹类型错误
    HookError = -24,                // Hook错误
    PrfModeHomeError = -25,         // 回零错误
    UnknownError = -26,             // 未知错误
    CrdHostFifoRunError = -27,      // 主机FIFO运行错误
    CrdFifo1RunError = -28,         // FIFO1运行错误
    CrdAxisMapSameError = -29,      // 轴表设定重复
    CrdFifoOverflowError = -30,     // FIFO溢出错误

    // API层错误码
    ApiErrorOpen = 100,             // 打开设备错误
    ApiErrorOutRange = 101,         // 参数超出范围
    ApiErrorType = 102,             // 类型错误
    ApiErrorCrdDemesion = 103,      // 坐标系维度错误
    ApiErrorCrdData = 104,          // 坐标系数据错误
    ApiErrorCrdMode = 105,          // 坐标系模式错误
    ApiErrorCrdRun = 106,           // 坐标系运行错误
    ApiErrorCrdStop = 107,          // 坐标系停止错误
    ApiErrorCrdPause = 108,         // 坐标系暂停错误
    ApiErrorCrdData2 = 109,         // 坐标系数据错误2
    ApiErrorCrdBufFull = 110,       // 坐标系缓冲区满
    ApiErrorCrdBufEmpty = 111,      // 坐标系缓冲区空
    ApiErrorCrdBufOverflow = 112,   // 坐标系缓冲区溢出
    ApiErrorCrdBufUnderflow = 113,  // 坐标系缓冲区下溢
    ApiErrorCrdBufData = 114,       // 坐标系缓冲区数据错误
    ApiErrorCrdBufDataType = 115,   // 坐标系缓冲区数据类型错误
    ApiErrorCrdBufDataSubType = 116, // 坐标系缓冲区数据子类型错误
    ApiErrorPrm = 117               // 参数错误
};

/**
 * @brief 轴状态枚举类
 * 将原来的位标志转换为枚举类型
 */
enum class AxisStatus : uint16_t {
    None = 0,                  // 无状态
    Enable = 1 << 0,           // 使能
    PositiveLimit = 1 << 1,    // 正限位
    NegativeLimit = 1 << 2,    // 负限位
    Origin = 1 << 3,           // 原点
    InPosition = 1 << 4,       // 到位
    Moving = 1 << 5,           // 运动中
    Alarm = 1 << 6,            // 报警
    Emergency = 1 << 7,        // 急停
    Reset = 1 << 8,            // 复位中
    Angle = 1 << 9             // 角度识别
};

// 启用按位操作符
inline AxisStatus operator|(AxisStatus a, AxisStatus b) {
    return static_cast<AxisStatus>(static_cast<uint16_t>(a) | static_cast<uint16_t>(b));
}

inline AxisStatus operator&(AxisStatus a, AxisStatus b) {
    return static_cast<AxisStatus>(static_cast<uint16_t>(a) & static_cast<uint16_t>(b));
}

inline bool operator!(AxisStatus a) {
    return static_cast<uint16_t>(a) == 0;
}

/**
 * @brief 错误类别枚举
 */
enum class ErrorCategory {
    None,           // 无错误
    System,         // 系统错误
    Communication,  // 通信错误
    Motion,         // 运动控制错误
    Axis,           // 轴错误
    IO,             // IO错误
    Parameter,      // 参数错误
    Configuration,  // 配置错误
    Unknown         // 未知错误
};

/**
 * @brief 系统状态枚举
 */
enum class SystemStatus {
    Disconnected,   // 未连接
    Connecting,     // 连接中
    Connected,      // 已连接
    Disconnecting,  // 断开连接中
    Initializing,   // 初始化中
    Ready,          // 就绪
    Running,        // 运行中
    Error,          // 错误
    Stopping,       // 停止中
    Stopped         // 已停止
};

/**
 * @brief 获取错误码对应的错误信息
 * @param code 错误码
 * @return 错误信息
 */
std::string getMotionErrorMessage(MotionErrorCode code);

/**
 * @brief 获取错误码对应的详细错误信息
 * @param code 错误码
 * @return 详细错误信息，包含错误码和错误描述
 */
std::string getMotionErrorDetails(MotionErrorCode code);

/**
 * @brief 获取错误码对应的错误类别
 * @param code 错误码
 * @return 错误类别
 */
ErrorCategory getErrorCategory(MotionErrorCode code);

/**
 * @brief 获取轴状态对应的描述
 * @param status 轴状态
 * @return 轴状态描述
 */
std::string getAxisStatusDescription(AxisStatus status);

/**
 * @brief 获取系统状态对应的描述
 * @param status 系统状态
 * @return 系统状态描述
 */
std::string getSystemStatusDescription(SystemStatus status);

/**
 * @brief 现代C++风格的运动控制API封装类
 * 使用组合方式包含MotionConn对象，提供更符合C++风格的接口
 */
// 轴状态监控回调函数类型
using AxisStatusCallback = std::function<void(short, AxisStatus)>;

// 错误处理回调函数类型
using ErrorCallback = std::function<void(MotionErrorCode, const std::string&)>;

class MotionAPI {
public:
    /**
     * @brief 默认构造函数
     */
    MotionAPI();

    /**
     * @brief 从MotionConn构造
     * @param conn MotionConn对象
     */
    explicit MotionAPI(const MotionConn& conn);

    /**
     * @brief 析构函数
     */
    ~MotionAPI();

    /**
     * @brief 设置日志回调函数
     * 
     * 将日志回调配置传递给底层的MotionConn对象。
     * @param callback 用户提供的回调函数指针。
     * @param userData 将在回调时传回的用户自定义数据。
     */
    void setLogCallback(LogCallback callback, void* userData);

    /**
     * @brief 打开板卡
     * @param ip IP地址
     * @param port 端口号
     * @return 错误码
     */
    MotionErrorCode openBoard(const std::string& ip, int port);

    /**
     * @brief 关闭板卡
     * @return 错误码
     */
    MotionErrorCode closeBoard();

    /**
     * @brief 重置板卡
     * @return 错误码
     */
    MotionErrorCode resetBoard();

    //=== 轴控制相关函数 ===//

    /**
     * @brief 设置轴参数
     * @param crd 坐标系编号
     * @param axisMap 轴映射数组
     * @param axisDir 轴方向数组
     * @param velMax 最大速度数组
     * @param accMax 最大加速度数组
     * @param positive 正限位数组
     * @param negative 负限位数组
     * @return 错误码
     */
    MotionErrorCode setAxisPrm(short crd,
                              const std::array<short, MAX_CRD_AXIS>& axisMap,
                              const std::array<short, MAX_CRD_AXIS>& axisDir,
                              const std::array<int32_t, MAX_CRD_AXIS>& velMax,
                              const std::array<int32_t, MAX_CRD_AXIS>& accMax,
                              const std::array<int32_t, MAX_CRD_AXIS>& positive,
                              const std::array<int32_t, MAX_CRD_AXIS>& negative);

    /**
     * @brief 使能轴
     * @param axis 轴号
     * @return 错误码
     */
    MotionErrorCode axisOn(short axis);

    /**
     * @brief 关闭轴使能
     * @param axis 轴号
     * @return 错误码
     */
    MotionErrorCode axisOff(short axis);

    /**
     * @brief 设置JOG模式
     * @param crd 坐标系编号
     * @return 错误码
     */
    MotionErrorCode setJogMode(short crd);

    /**
     * @brief 设置JOG参数
     * @param crd 坐标系编号
    * @param Maxvel 最大速度
     * @param acc 加速度
     * @param dec 减速度
     * @param rate 倍率
     * @return 错误码
     */
    MotionErrorCode setJogPrm(short crd, int32_t Maxvel, int32_t acc, int32_t dec, int32_t rate);

    /**
     * @brief JOG运动更新
     * @param axis 轴号
     * @param dir 方向，1:正向，-1:负向，0:停止
     * @return 错误码
     */
    MotionErrorCode jogUpdate(short axis, short dir);

    /**
     * @brief 清除轴报警
     * @param axis 轴号
     * @return 错误码
     */
    MotionErrorCode axisClearAlarm(short axis);

    /**
     * @brief 获取错误码
     * @param axis 轴序号
     * @return 错误码，如果发生错误则返回空
     */
    Optional<short> getErrorCode(short axis) const;

    /**
     * @brief 获取坐标系位置
     * @param crd 坐标系编号
     * @return 坐标系位置数组，如果发生错误则返回空
     */
    Optional<std::array<double, 2>> getCrdPos(short crd) const;

    /**
     * @brief 获取轴位置
     * @param axis 轴号
     * @return 轴位置，如果发生错误则返回空
     */
    Optional<double> getAxisPos(short axis) const;

    /**
     * @brief 获取轴状态
     * @param axis 轴号
     * @return 轴状态，如果发生错误则返回空
     */
    Optional<AxisStatus> getAxisStatus(short axis) const;

    /**
     * @brief 设置设备输出
     * @param output 输出值
     * @return 错误码
     */
    MotionErrorCode setDeviceOutput(int32_t output);

    /**
     * @brief 获取设备输出
     * @return 设备输出值，如果发生错误则返回空
     */
    Optional<int32_t> getDeviceOutput() const;

    /**
     * @brief 获取设备输入
     * @return 设备输入值，如果发生错误则返回空
     */
    Optional<int32_t> getDeviceInput() const;

    //=== IO相关函数 ===//

    /**
     * @brief 设置高速IO参数
     * @param ioNum IO编号
     * @param duty 占空比(us)
     * @param period 触发周期(us)，如果duty==period，则为常开模式
     * @return 错误码
     */
    MotionErrorCode setSpeedIOParam(short ioNum, short duty, short period);

    /**
     * @brief 手动控制高速IO电平
     * @param ioNum IO编号
     * @param switchState 开关状态，0:关 1:开
     * @return 错误码
     */
    MotionErrorCode setSpeedIOState(short ioNum, short switchState);

    /**
     * @brief 设置IO脉冲使能
     * @param crd 坐标系编号
     * @param ioNum IO编号
     * @param ioEnable 使能状态
     * @return 错误码
     */
    MotionErrorCode setIOPulseEnable(short crd, short ioNum, short ioEnable);

    /**
     * @brief 设置IO脉冲状态
     * @param crd 坐标系编号
     * @param ioNum IO编号
     * @param ioState IO状态
     * @return 错误码
     */
    MotionErrorCode setIOPulseState(short crd, short ioNum, short ioState);

    /**
     * @brief 设置IO脉冲触发
     * @param crd 坐标系编号
     * @param ioNum IO编号
     * @param ioTrigger 触发状态
     * @return 错误码
     */
    MotionErrorCode setIOPulseTrigger(short crd, short ioNum, short ioTrigger);

    /**
     * @brief 获取FPGA版本
     * @return FPGA版本号，如果发生错误则返回空
     */
    Optional<short> getFpgaVersion() const;

    /**
     * @brief 发送速度IO点位
     * @param crd 坐标系编号
     * @param points 点位数组
     * @return 错误码
     */
    MotionErrorCode sendSpeedIOPoint(short crd, const std::vector<SpeedIOPoint>& points);

    /**
     * @brief 速度IO使能
     * @param crd 坐标系编号
     * @param enable 使能状态
     * @param ioNum IO编号
     * @return 错误码
     */
    MotionErrorCode speedIOEnable(short crd, short enable, short ioNum);

    /**
     * @brief 清除速度IO点位
     * @param crd 坐标系编号
     * @return 错误码
     */
    MotionErrorCode speedIOClearPoint(short crd);

    //=== 系统状态监控函数 ===//

    /**
     * @brief 获取系统状态
     * @return 系统状态
     */
    SystemStatus getSystemStatus() const;

    /**
     * @brief 注册系统状态变化回调函数
     * @param callback 回调函数
     * @return 回调函数ID，用于取消注册
     */
    int registerStatusCallback(std::function<void(SystemStatus)> callback);

    /**
     * @brief 取消注册系统状态变化回调函数
     * @param callbackId 回调函数ID
     * @return 是否成功取消注册
     */
    bool unregisterStatusCallback(int callbackId);

    /**
     * @brief 获取系统诊断信息
     * @return 诊断信息映射表
     */
    std::map<std::string, std::string> getDiagnosticInfo() const;

    /**
     * @brief 检查系统是否处于错误状态
     * @return 是否处于错误状态
     */
    bool isSystemInError() const;

    /**
     * @brief 清除系统错误
     * @return 错误码
     */
    MotionErrorCode clearSystemError();

    /**
     * @brief 获取最后一次错误的详细信息
     * @return 错误详细信息
     */
    std::string getLastErrorDetails() const;

    //=== 点位运动相关函数 ===//

    /**
     * @brief 设置点位模式
     * @param crd 坐标系编号
     * @return 错误码
     */
    MotionErrorCode setCrdTrapMode(short crd);

    /**
     * @brief 设置点位参数
     * @param crd 坐标系编号
     * @param prm 点位参数
     * @return 错误码
     */
    MotionErrorCode setCrdTrapPrm(short crd, const TTrapPrm& prm);

    /**
     * @brief 更新点位参数
     * @param crd 坐标系编号
     * @return 错误码
     */
    MotionErrorCode crdTrapUpdate(short crd);

    /**
     * @brief 设置轴点位模式
     * @param crd 坐标系编号
     * @param axis 轴号
     * @return 错误码
     */
    MotionErrorCode setAxisTrapMode(short crd, short axis);

    /**
     * @brief 设置轴点位参数
     * @param crd 坐标系编号
     * @param axis 轴号
     * @param prm 点位参数
     * @return 错误码
     */
    MotionErrorCode setAxisTrapPrm(short crd, short axis, const TTrapPrm& prm);

    /**
     * @brief 更新轴点位参数
     * @param crd 坐标系编号
     * @return 错误码
     */
    MotionErrorCode axisTrapUpdate(short crd);


    //=== 坐标系插补相关函数 ===//

    /**
     * @brief 设置坐标系参数
     * @param crd 坐标系编号
     * @param prm 坐标系参数
     * @return 错误码
     */
    MotionErrorCode setCrdPrm(short crd, const TCrdPrm& prm);





    /**
     * @brief 启动坐标系插补
     * @param crd 坐标系编号
     * @return 错误码
     */
    MotionErrorCode crdStart(short crd);

    /**
     * @brief 停止坐标系插补
     * @param mask 掩码
     * @param option 选项
     * @param stopType 停止类型
     * @return 错误码
     */
    MotionErrorCode crdStop(short mask, short option, short stopType);

    /**
     * @brief 暂停坐标系插补
     * @param crd 坐标系编号
     * @return 错误码
     */
    MotionErrorCode crdPause(short crd);

    /**
     * @brief 直线插补
     * @param crd 坐标系编号
     * @param x X轴终点位置
     * @param y Y轴终点位置
     * @param synVel 合成速度
     * @param synAcc 合成加速度
     * @param velEnd 终点速度
     * @return 错误码
     */
    MotionErrorCode ln(short crd, int32_t x, int32_t y, double synVel, double synAcc, double velEnd);

    /**
     * @brief 圆弧插补（三点法）
     * @param crd 坐标系编号
     * @param p1 第一点坐标
     * @param p2 第二点坐标
     * @param p3 第三点坐标
     * @param radius 半径
     * @param circleDir 圆弧方向
     * @param synVel 合成速度
     * @param synAcc 合成加速度
     * @param velEnd 终点速度
     * @return 错误码
     */
    MotionErrorCode arcXY3Point(short crd, const std::array<int32_t, 3>& p1, const std::array<int32_t, 3>& p2,
                               const std::array<int32_t, 3>& p3, double radius, short circleDir,
                               double synVel, double synAcc, double velEnd);

    /**
     * @brief 圆弧插补（终点半径法）
     * @param crd 坐标系编号
     * @param x X轴终点位置
     * @param y Y轴终点位置
     * @param radius 半径
     * @param circleDir 圆弧方向
     * @param synVel 合成速度
     * @param synAcc 合成加速度
     * @param velEnd 终点速度
     * @return 错误码
     */
    MotionErrorCode arcXYR(short crd, int32_t x, int32_t y, double radius, short circleDir,
                          double synVel, double synAcc, double velEnd);

    /**
     * @brief 圆弧插补（终点圆心法）
     * @param crd 坐标系编号
     * @param x X轴终点位置
     * @param y Y轴终点位置
     * @param xCenter X轴圆心
     * @param yCenter Y轴圆心
     * @param circleDir 圆弧方向
     * @param synVel 合成速度
     * @param synAcc 合成加速度
     * @param velEnd 终点速度
     * @return 错误码
     */
    MotionErrorCode arcXYC(short crd, int32_t x, int32_t y, double xCenter, double yCenter,
                          short circleDir, double synVel, double synAcc, double velEnd);

    //=== 前瞻相关函数 ===//

    /**
     * @brief 初始化前瞻
     * @param crd 坐标系编号
     * @param accMax 最大加速度
     * @param count 前瞻段数
     * @return 错误码
     */
    MotionErrorCode initLookAhead(short crd, double accMax, short count);

    /**
     * @brief 关闭前瞻
     * @param crd 坐标系编号
     * @return 错误码
     */
    MotionErrorCode closeLookAhead(short crd);

    /**
     * @brief 获取原始TADMotionConn指针
     * @return TADMotionConn指针
     */
    TADMotionConn* getHandle() const;

    //=== 轴状态监控函数 ===//

    /**
     * @brief 注册轴状态变化回调函数
     * @param callback 回调函数
     * @return 回调函数ID，用于取消注册
     */
    int registerAxisStatusCallback(AxisStatusCallback callback);

    /**
     * @brief 取消注册轴状态变化回调函数
     * @param callbackId 回调函数ID
     * @return 是否成功取消注册
     */
    bool unregisterAxisStatusCallback(int callbackId);

    /**
     * @brief 开始轴状态监控
     * @param axis 轴号
     * @param intervalMs 监控间隔(毫秒)
     * @return 错误码
     */
    MotionErrorCode startAxisMonitoring(short axis, int intervalMs = 100);

    /**
     * @brief 停止轴状态监控
     * @param axis 轴号
     * @return 错误码
     */
    MotionErrorCode stopAxisMonitoring(short axis);

    /**
     * @brief 检查轴是否处于特定状态
     * @param axis 轴号
     * @param status 要检查的状态
     * @return 是否处于指定状态，如果发生错误则返回false
     */
    bool isAxisInStatus(short axis, AxisStatus status) const;

    /**
     * @brief 等待轴达到特定状态
     * @param axis 轴号
     * @param status 要等待的状态
     * @param timeoutMs 超时时间(毫秒)
     * @return 是否在超时前达到指定状态
     */
    bool waitForAxisStatus(short axis, AxisStatus status, int timeoutMs = 5000);

    //=== 错误处理函数 ===//

    /**
     * @brief 注册错误处理回调函数
     * @param callback 回调函数
     * @return 回调函数ID，用于取消注册
     */
    int registerErrorCallback(ErrorCallback callback);

    /**
     * @brief 取消注册错误处理回调函数
     * @param callbackId 回调函数ID
     * @return 是否成功取消注册
     */
    bool unregisterErrorCallback(int callbackId);

    /**
     * @brief 设置日志级别
     * @param level 日志级别
     */
    void setLogLevel(LogLevel level);

    /**
     * @brief 获取当前日志级别
     * @return 日志级别
     */
    LogLevel getLogLevel() const;

    /**
     * @brief 设置日志文件路径
     * @param logFilePath 日志文件路径
     * @return 是否设置成功
     */
    bool setLogFilePath(const std::string& logFilePath);

private:
    MotionConn m_conn;                                  // MotionConn对象
    SystemStatus m_systemStatus;                        // 系统状态
    std::string m_lastErrorDetails;                     // 最后一次错误的详细信息
    std::map<int, std::function<void(SystemStatus)>> m_statusCallbacks;  // 系统状态回调函数
    std::map<int, AxisStatusCallback> m_axisStatusCallbacks;             // 轴状态回调函数
    std::map<int, ErrorCallback> m_errorCallbacks;                       // 错误处理回调函数
    int m_nextCallbackId;                               // 下一个回调函数ID
    std::mutex m_callbackMutex;                         // 回调函数互斥锁

    /**
     * @brief 更新系统状态
     * @param newStatus 新状态
     */
    void updateSystemStatus(SystemStatus newStatus);

    /**
     * @brief 记录错误信息
     * @param code 错误码
     * @param operation 操作名称
     */
    void logError(MotionErrorCode code, const std::string& operation);
};

#endif // MOTION_API_H
