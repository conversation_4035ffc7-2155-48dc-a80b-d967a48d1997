#pragma once
#ifndef LOOKAHEAD_BUFFER_H
#define LOOKAHEAD_BUFFER_H

#include <vector>
#include <memory>
#include <stdexcept>
#include <algorithm>
#include "motion_types.h"

/**
 * @brief 前瞻缓冲区异常类
 */
class LookAheadBufferException : public std::runtime_error {
public:
    explicit LookAheadBufferException(const std::string& message) : std::runtime_error(message) {}
};

/**
 * @brief 前瞻缓冲区类
 * 使用std::vector替代原来的裸指针数组，提供更安全的内存管理
 */
class LookAheadBuffer {
public:
    /**
     * @brief 构造函数
     * @param size 缓冲区大小
     * @throws std::invalid_argument 如果size为0
     */
    explicit LookAheadBuffer(size_t size = 200) {
        if (size == 0) {
            throw std::invalid_argument("Buffer size cannot be 0");
        }
        resize(size);
    }

    /**
     * @brief 析构函数
     */
    ~LookAheadBuffer() {
    }

    /**
     * @brief 重新设置缓冲区大小
     * @param size 新的缓冲区大小
     * @throws std::invalid_argument 如果size为0
     */
    void resize(size_t size) {
        if (size == 0) {
            throw std::invalid_argument("Buffer size cannot be 0");
        }

        try {
            m_buffer.resize(size);
            // 初始化缓冲区，避免未初始化的内存
            std::fill(m_buffer.begin(), m_buffer.end(), TCrdData{});
        } catch (const std::exception& e) {
            // 在这里不记录日志，直接向上抛出异常
            throw;
        }
    }

    /**
     * @brief 清空缓冲区
     */
    void clear() {
        try {
            std::fill(m_buffer.begin(), m_buffer.end(), TCrdData{});
        } catch (const std::exception& e) {
            // 在这里不记录日志，直接向上抛出异常
            throw;
        }
    }

    /**
     * @brief 获取缓冲区大小
     * @return 缓冲区大小
     */
    size_t size() const noexcept {
        return m_buffer.size();
    }

    /**
     * @brief 获取指定索引的元素
     * @param index 索引
     * @return 元素引用
     * @throws std::out_of_range 如果索引越界
     */
    TCrdData& at(size_t index) {
        try {
            return m_buffer.at(index);
        } catch (const std::out_of_range& e) {
            // LOG_ERROR("Buffer index out of range: " + std::to_string(index) + ", size: " + std::to_string(size()));
            throw;
        }
    }

    /**
     * @brief 获取指定索引的元素（常量版本）
     * @param index 索引
     * @return 元素常量引用
     * @throws std::out_of_range 如果索引越界
     */
    const TCrdData& at(size_t index) const {
        try {
            return m_buffer.at(index);
        } catch (const std::out_of_range& e) {
            // LOG_ERROR("Buffer index out of range: " + std::to_string(index) + ", size: " + std::to_string(size()));
            throw;
        }
    }

    /**
     * @brief 获取缓冲区首地址
     * @return 缓冲区首地址
     * @throws LookAheadBufferException 如果缓冲区为空
     */
    TCrdData* data() {
        if (m_buffer.empty()) {
            throw LookAheadBufferException("Buffer is empty");
        }
        return m_buffer.data();
    }

    /**
     * @brief 获取缓冲区首地址（常量版本）
     * @return 缓冲区首地址
     * @throws LookAheadBufferException 如果缓冲区为空
     */
    const TCrdData* data() const {
        if (m_buffer.empty()) {
            throw LookAheadBufferException("Buffer is empty");
        }
        return m_buffer.data();
    }

    /**
     * @brief 获取指定偏移的元素指针
     * @param offset 偏移量
     * @return 元素指针
     * @throws LookAheadBufferException 如果缓冲区为空
     */
    TCrdData* getPointer(size_t offset) {
        if (m_buffer.empty()) {
            throw LookAheadBufferException("Buffer is empty");
        }

        // 处理越界情况
        if (offset >= m_buffer.size()) {
            offset = offset % m_buffer.size();
        }

        return &m_buffer[offset];
    }

    /**
     * @brief 移动指针
     * @param ptr 当前指针
     * @param n 移动步数（正数向前，负数向后）
     * @return 移动后的指针
     * @throws LookAheadBufferException 如果ptr不在缓冲区内或缓冲区为空
     */
    TCrdData* movePointer(TCrdData* ptr, int n) {
        if (m_buffer.empty()) {
            throw LookAheadBufferException("Buffer is empty");
        }

        if (!ptr) {
            throw LookAheadBufferException("Null pointer");
        }

        // 计算当前指针在缓冲区中的偏移量
        const TCrdData* bufferStart = data();
        const TCrdData* bufferEnd = bufferStart + m_buffer.size();

        // 检查指针是否在缓冲区范围内
        if (ptr < bufferStart || ptr >= bufferEnd) {
            throw LookAheadBufferException("Pointer out of range");
        }

        size_t offset = static_cast<size_t>(ptr - bufferStart);

        // 计算移动后的偏移量
        if (n >= 0) {
            offset = (offset + static_cast<size_t>(n)) % m_buffer.size();
        } else {
            // 处理负数移动
            size_t absN = static_cast<size_t>(-n);
            if (absN > offset) {
                offset = m_buffer.size() - (absN - offset) % m_buffer.size();
                if (offset == m_buffer.size()) {
                    offset = 0;
                }
            } else {
                offset -= absN;
            }
        }

        return &m_buffer[offset];
    }

    /**
     * @brief 检查指针是否在缓冲区范围内
     * @param ptr 要检查的指针
     * @return 是否在范围内
     */
    bool isPointerInRange(const TCrdData* ptr) const noexcept {
        if (m_buffer.empty() || !ptr) {
            return false;
        }

        const TCrdData* bufferStart = m_buffer.data();
        const TCrdData* bufferEnd = bufferStart + m_buffer.size();

        return (ptr >= bufferStart && ptr < bufferEnd);
    }

private:
    std::vector<TCrdData> m_buffer; // 缓冲区
};

#endif // LOOKAHEAD_BUFFER_H
