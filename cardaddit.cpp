﻿//#include <Windows.h>
#include <string.h>
#include "math.h"
#include "cmdcode.h"
#include "ioctrl.h"
#include "card.h"
#include "crdmath.h"
#include "admc.h"

double gTransitionL;
// TTransitionPrm gTransitionPrm[MAX_CRD];

//转接处理
// short TransitionProcessing(short crd, TCrdData *pCrdData)
//{
//	short rtn, bezierReturn, i;
//	short crdIndex = crd - 1;
//	Point controlPoint[7];
//	double firstPos[5];
//	double tempMaxVel = 0;
//	if ((gTransitionPrm[crdIndex].enable == false) || (pCrdData == NULL) //不转接 或者 无数据
//		|| (FALSE == IsIntpLineOrArcType(pCrdData->motionType))) //当前段不是直线和平面圆弧。
//	{
//		if (gTransitionPrm[crdIndex].isPreDataExist == TRUE)
//		{
//			rtn = InsertNewLine(crd, &gTransitionPrm[crdIndex].data1, fifo);//发送第一段
//			if (CMD_SUCCESS != rtn)
//			{
//				return rtn;
//			}
//			gTransitionPrm[crdIndex].isPreDataExist = false;
//		}
//		//没有转接过渡，直接插入到前瞻模块处理
//		rtn = InsertNewLine(crd, pCrdData, fifo);
//		if (CMD_SUCCESS != rtn)
//		{
//			return rtn;
//		}
//		return CMD_SUCCESS;
//	}
//	else if ((gTransitionPrm[crdIndex].enable == TRUE) && (gTransitionPrm[crdIndex].isPreDataExist == TRUE))
//	{
//		//第一段数据后面没有接转接段，第一段数据发出。
//		if ((0 == gTransitionPrm[crdIndex].data1.transL) || (false ==
//IsIntpLineOrArcType(gTransitionPrm[crdIndex].data1.motionType))) //前一段不是直线和平面圆弧、螺旋线。
//		{
//			//rtn = InsertNewLine(crd, &gTransitionPrm[crdIndex].data1, fifo);
//			if (CMD_SUCCESS != rtn)
//			{
//				return rtn;
//			}
//			//更新数据位置
//			for (i = 0; i < MAX_CRD_AXIS; i++)
//			{
//				gTransitionPrm[crdIndex].lastPos[i] = gTransitionPrm[crdIndex].data1.pos[i];
//			}
//			gTransitionPrm[crdIndex].isPreDataExist = false;
//		}
//	}
//	// If not skipped, start the transition process
//	//save current data and import the path data
//	memcpy(&gTransitionPrm[crdIndex].data0, pCrdData, sizeof(TCrdData));
//	//gTransitionPrm[crdIndex].m_previous_data_exist = 1;
//
//	// Check whether to wait or to begin transition
//	if (!gTransitionPrm[crdIndex].isPreDataExist)//Wait if there is no previous data
//	{
//		memcpy(&gTransitionPrm[crdIndex].data1, &gTransitionPrm[crdIndex].data0, sizeof(TCrdData));  // move the current data to
//the previous one 		gTransitionPrm[crdIndex].isPreDataExist = TRUE; // set existing indicator true to begin transition next time
//		return CMD_SUCCESS;
//	}
//
//	//Begin transition
//	short transitionComplete = TRUE;
//	//当前段的起点位置。
//	for (i = 0; i < MAX_CRD_AXIS; i++)
//	{
//		firstPos[i] = gTransitionPrm[crdIndex].data1.startPos[i];
//	}
//
//	gTransitionL = gTransitionPrm[crdIndex].data1.transL;
//
//	if ((gTransitionPrm[crdIndex].data1.motionType == CRD_CMD_TYPE_LINE)
//		&& (gTransitionPrm[crdIndex].data0.motionType == CRD_CMD_TYPE_LINE))
//	{
//		//bezierReturn = BezierTransition(firstPos, gTransitionPrm[crdIndex].data1.pos, gTransitionPrm[crdIndex].data0.pos,
//controlPoint);
//	}
//	else if (((gTransitionPrm[crdIndex].data1.motionType == CRD_CMD_TYPE_ARC) || (gTransitionPrm[crdIndex].data1.motionType ==
//CRD_CMD_TYPE_3DARC))
//		&& (gTransitionPrm[crdIndex].data0.motionType == CRD_CMD_TYPE_LINE))
//	{
//		double pos[3];
//		GetCirclePoint(gTransitionPrm[crdIndex].data1.pos, gTransitionPrm[crdIndex].data1.endVector, pos);
//		//bezierReturn = BezierTransition(pos, gTransitionPrm[crdIndex].data1.pos, gTransitionPrm[crdIndex].data0.pos,
//controlPoint);
//	}
//	else if ((gTransitionPrm[crdIndex].data1.motionType == CRD_CMD_TYPE_LINE)
//		&& ((gTransitionPrm[crdIndex].data0.motionType == CRD_CMD_TYPE_ARC) || (gTransitionPrm[crdIndex].data0.motionType ==
//CRD_CMD_TYPE_3DARC)))
//	{
//		double pos[3], startVector[3];
//		for (i = 0; i < 3; i++)
//		{
//			startVector[i] = (-1)*gTransitionPrm[crdIndex].data0.startVector[i];
//		}
//		GetCirclePoint(gTransitionPrm[crdIndex].data1.pos, startVector, pos);
//		//bezierReturn = BezierTransition(firstPos, gTransitionPrm[crdIndex].data1.pos, pos, controlPoint);
//	}
//	else if (((gTransitionPrm[crdIndex].data1.motionType == CRD_CMD_TYPE_ARC) || (gTransitionPrm[crdIndex].data1.motionType ==
//CRD_CMD_TYPE_3DARC))
//		&& ((gTransitionPrm[crdIndex].data0.motionType == CRD_CMD_TYPE_ARC) || (gTransitionPrm[crdIndex].data0.motionType ==
//CRD_CMD_TYPE_3DARC)))
//	{
//		//计算前一个点
//		GetCirclePoint(gTransitionPrm[crdIndex].data1.pos, gTransitionPrm[crdIndex].data1.endVector, firstPos);
//		//计算后一个点
//		double pos[3], startVector[3];
//		for (i = 0; i < 3; i++)
//		{
//			startVector[i] = (-1)*gTransitionPrm[crdIndex].data0.startVector[i];
//		}
//		GetCirclePoint(gTransitionPrm[crdIndex].data1.pos, startVector, pos);
//		//bezierReturn = BezierTransition(firstPos, gTransitionPrm[crdIndex].data1.pos, pos, controlPoint);
//	}
//	//发送第一段数据
//	gTransitionPrm[crdIndex].data1.velEnd = gTransitionPrm[crdIndex].data1.vel;
//	/*if(gTransitionPrm[crdIndex].data1.vel != gTransitionPrm[crdIndex].data0.vel)//假如两段的进给速度不相等，取消bezier过渡
//	{
//	bezierReturn = -2;
//	gTransitionPrm[crdIndex].data1.velEnd = 0;
//	}*/
//	//角度过小、过大 放弃转接
//	if (bezierReturn != CMD_SUCCESS)
//	{
//		transitionComplete = false;
//		//角度过小，末速度设为0
//		if (bezierReturn == -2)
//		{
//			gTransitionPrm[crdIndex].data1.velEnd = 0;
//		}
//	}
//
//	rtn = InsertNewLine(crd, &gTransitionPrm[crdIndex].data1, fifo);
//	if (CMD_SUCCESS != rtn)
//	{
//		return rtn;
//	}
//
//	tempMaxVel = gTransitionPrm[crdIndex].data1.vel > gTransitionPrm[crdIndex].data0.vel ? gTransitionPrm[crdIndex].data1.vel :
//gTransitionPrm[crdIndex].data0.vel;
//
//	//if (false == CrdBezierPrmCheck(gTransitionL, tempMaxVel))
//	//{
//	//	gTransitionPrm[crdIndex].data1.velEnd = 0;
//	//	transitionComplete = false;
//	//}
//
//	//发送第二段数据
//	if (TRUE == transitionComplete)
//	{
//		memcpy(&gTransitionPrm[crdIndex].tempData, &gTransitionPrm[crdIndex].data1, sizeof(TCrdData));
//		//bezier第一个点，放在pos中,转接类型设定为CMD_Bezier_TYPE
//		gTransitionPrm[crdIndex].tempData.motionType = CRD_CMD_TYPE_BEZIER;
//		gTransitionPrm[crdIndex].tempData.pos[0] = gTransitionPrm[crdIndex].data1.pos[0];
//		gTransitionPrm[crdIndex].tempData.pos[1] = gTransitionPrm[crdIndex].data1.pos[1];
//		gTransitionPrm[crdIndex].tempData.pos[2] = gTransitionPrm[crdIndex].data1.pos[2];
//
//		gTransitionPrm[crdIndex].tempData.center[0] = controlPoint[5].x;
//		gTransitionPrm[crdIndex].tempData.center[1] = controlPoint[5].y;
//		gTransitionPrm[crdIndex].tempData.center[2] = controlPoint[5].z;
//
//		gTransitionPrm[crdIndex].tempData.crdTrans[0] = controlPoint[2].x;
//		gTransitionPrm[crdIndex].tempData.crdTrans[1] = controlPoint[2].y;
//		gTransitionPrm[crdIndex].tempData.crdTrans[2] = controlPoint[2].z;
//
//		gTransitionPrm[crdIndex].tempData.crdTrans[3] = controlPoint[3].x;
//		gTransitionPrm[crdIndex].tempData.crdTrans[4] = controlPoint[3].y;
//		gTransitionPrm[crdIndex].tempData.crdTrans[5] = controlPoint[3].z;
//
//		gTransitionPrm[crdIndex].tempData.crdTrans[6] = controlPoint[4].x;
//		gTransitionPrm[crdIndex].tempData.crdTrans[7] = controlPoint[4].y;
//		gTransitionPrm[crdIndex].tempData.crdTrans[8] = controlPoint[4].z;
//		//插值点的数量。
//		gTransitionPrm[crdIndex].tempData.height = (int32_t)gTransitionL;
//		gTransitionPrm[crdIndex].tempData.vel = gTransitionPrm[crdIndex].data0.vel;
//
//		//将贝塞尔中的buf指令清除
//		gTransitionPrm[crdIndex].tempData.bufData.triggerData.type = 0;
//		gTransitionPrm[crdIndex].tempData.bufData.triggerData.triCount = 0;
//		gTransitionPrm[crdIndex].tempData.bufData.triggerData.preOffset = 0;
//		gTransitionPrm[crdIndex].tempData.bufData.triggerData.reserve0 = 0;
//		gTransitionPrm[crdIndex].tempData.bufData.triggerData.reserve1 = 0;
//
//		rtn = InsertNewLine(crd, &gTransitionPrm[crdIndex].tempData, fifo);
//		if (CMD_SUCCESS != rtn)
//		{
//			return rtn;
//		}
//	}
//
//	//数据标志还原
//	gTransitionPrm[crdIndex].isPreDataExist = TRUE;
//	memcpy(&gTransitionPrm[crdIndex].data1, &gTransitionPrm[crdIndex].data0, sizeof(TCrdData));
//	return CMD_SUCCESS;
//}
