# ADMotion 动态库接口说明文档 (修订版)

## 1. 概述与架构

本说明旨在深入分析 `ADMotion` 动态库的C++接口及其底层架构。该库设计精良，采用分层架构，充分利用了C++11的特性，为不同需求的开发者提供了从底层到高层的多套接口。

### 1.1. 库的核心架构

该库可以被清晰地划分为四个层次：

-   **第0层 - 导出层 (`admc_export.h`)**: 定义了 `__declspec(dllexport)` 宏，是整个动态库API能够被外部程序发现和使用的基础。
-   **第1层 - 核心C接口层 (`admc_pci.h`)**: 库功能的纯C实现。提供了一系列稳定的、跨语言兼容的C函数，是整个库功能的核心。
-   **第2层 - C++连接与协议层 (`motion_conn.h`, `ADMotionPackage.h`)**:
    -   `motion_conn.h`：使用C++的RAII和智能指针封装了核心C接口的资源句柄，提供了基础的内存安全和易用性。
    -   `ADMotionPackage.h`：在连接层之上，进一步处理通信协议细节，并提供了一个C++风格的瘦包装 `TADMotion`。
-   **第3层 - 高级C++应用层 (`motion_api.h`)**: 最高级的封装 `MotionAPI` 类。它为C++开发者提供了最现代化、最安全、功能最丰富的接口。


```mermaid
graph TD
    subgraph 应用层
        A["C++ 应用程序"]
        C["其他语言应用 (C#, Python...)"]
    end

    subgraph ADMotion动态库
        subgraph "第3层: 高级C++应用层"
            B["<b>motion_api.h</b><br/>class MotionAPI"]
        end

        subgraph "第2层: C++连接与协议层"
            D["<b>motion_conn.h</b><br/>class MotionConn (RAII封装)"]
            E["<b>ADMotionPackage.h</b><br/>class TADMotion (C++瘦包装)"]
        end

        subgraph "第1层: 核心C接口层"
            F["<b>admc_pci.h</b><br/>API_OpenBoard(), API_AxisOn(), ..."]
        end
    end

    %% --- 连接关系 ---
    A --> B
    B --> D
    D --> F
    E --> F
    C --> F
```


## 2. 各层次接口详解

### 2.1. 第1层: 核心C接口 (`admc_pci.h`)

这是库最基础的接口，由一系列纯C函数构成，保证了最大的兼容性。

-   **文件**: `admc_pci.h`
-   **设计模式**:
    -   **纯C函数**: 所有接口都是用 `extern "C"` 和 `__stdcall` 声明的C函数，如 `API_OpenBoard`, `API_AxisOn`。
    -   **不透明句柄**: 通过 `TADMotionConn*` 句柄来管理状态，调用者需要负责创建 (`API_CreateBoard`) 和销毁 (`API_DeleteBoard`)。
-   **优点**: 稳定、高效、跨语言。任何支持C调用的环境都可以使用此接口。
-   **应用场景**:
    -   为上层C++封装提供基础。
    -   在C、C#、Python等非C++项目中直接调用。

### 2.2. 第2层: C++连接与协议层

这一层是对C接口的初步C++封装，解决了资源管理和协议解析的问题。

#### 2.2.1. `MotionConn`: RAII资源管理 (`motion_conn.h`)

-   **文件**: `motion_conn.h`
-   **设计模式**:
    -   **RAII封装**: `MotionConn` 类使用 `std::shared_ptr` 和自定义删除器包装了 `TADMotionConn*` 句柄。
    -   **职责单一**: 其核心职责就是安全地管理连接句柄的生命周期。
-   **优点**: 自动化资源管理，避免内存泄漏，提供基本的C++易用性。
-   **应用场景**: 作为更高层C++ API的基础组件，内部使用。

#### 2.2.2. `TADMotion`: C++瘦包装 (`ADMotionPackage.h`)

-   **文件**: `ADMotionPackage.h`
-   **设计模式**:
    -   **Inline包装**: `TADMotion` 类继承自 `TADMotionConn`，其成员函数大多是对核心C函数的 `inline` 包装。
    -   **协议暴露**: 暴露了通信协议的细节（如 `@@`, `$$` 包头）。
-   **优点**: 提供了比纯C接口更方便的C++调用语法。
-   **应用场景**: 需要关心通信协议细节，但又想使用C++语法的场景。

### 2.3. 第3层: 高级C++应用层 (`motion_api.h`)

这是为C++开发者设计的、功能最全面的现代化接口。

-   **文件**: `motion_api.h`
-   **设计模式**:
    -   **面向对象**: `MotionAPI` 类封装了所有功能。
    -   **组合优于继承**: 内部**包含**一个 `MotionConn` 对象来管理连接，而不是继承。
    -   **现代C++特性**: 大量使用 `std::function` 实现异步回调，使用强类型枚举，返回详细的错误码，并集成日志。
-   **优点**: 安全、易用、功能强大、代码可读性高。
-   **应用场景**: 所有新的C++项目的首选接口。

---

## 3. 接口对比与选择建议

| 特性 | `admc_pci.h` (核心C接口) | `TADMotion` (C++瘦包装) | `MotionAPI` (高级C++接口) |
| :--- | :--- | :--- | :--- |
| **语言** | 纯C | C++ | 现代C++ (11+) |
| **风格** | 面向过程 | 面向对象 (瘦包装) | 面向对象 (完全封装) |
| **资源管理** | 手动 (Create/Delete) | 半自动 | 全自动 (RAII) |
| **错误处理** | 返回错误码 | 返回错误码 | `enum class` 错误码 + 回调 |
| **异步** | 不支持 | 基于原始数据包回调 | `std::function` 状态/错误回调 |
| **易用性** | 低 | 中 | 高 |
| **灵活性** | 高 (接近底层) | 中 | 高 (功能全面) |

### 结论与建议

-   对于**新的C++应用程序**，毫无疑问应该选择 **`MotionAPI`** (`motion_api.h`)。它提供了无与伦比的安全性、易用性和开发效率。
-   对于需要与**现有C代码库集成**或在**C#、Python等语言中调用**的场景，应该使用 **`admc_pci.h`** 中定义的纯C接口。
-   **`TADMotion`** (`ADMotionPackage.h`) 处于两者之间，在现代C++项目中几乎没有使用它的理由，除非有非常特殊的需求要处理原始通信协议。

---

*（文档的其余部分，如核心数据结构和枚举的介绍，可以保留之前的版本，因为它们是所有上层API共享的基础。）*

## 4. 项目头文件结构

通过分析，库的主要接口由以下几个关键头文件定义：

-   **核心定义文件**:
    -   `motion_types.h`: 定义核心数据结构，如版本信息、插补数据等。
    -   `motion_enums.h`: 定义强类型枚举，如资源类型、运动模式等，增强了代码的可读性和类型安全。
    -   `motion_struct.h`: 定义运动参数结构体，如 JOG、梯形曲线运动的参数。
    -   `ErrorCode_Motion.h`: 提供将错误码转换为可读字符串的函数。
    -   `ADOSType.h`: 处理平台差异性，确保跨平台兼容。

-   **API接口文件**:
    -   `motion_api.h`: 定义了现代化的 `MotionAPI` C++ 封装类。
    -   `ADMotionPackage.h`: 定义了底层的 `TADMotion` C风格接口封装类和通信协议相关的数据结构。

---

## 5. 核心数据结构与枚举 (Types & Enums)

理解API前，首先需要了解其基础构建块。

### 5.1. 主要数据结构 (在 `motion_struct.h` 和 `motion_types.h` 中)

-   `struct TVersion`: 存储库和固件的版本信息。
-   `struct TCardInfo`: 描述运动控制卡的基本信息，如轴数、IO数等。
-   `struct TTrapPrm`: 用于点位运动（梯形加减速）的参数配置。
-   `struct TJogPrm`: 用于手动/JOG运动的参数配置。
-   `struct TCrdPrm`: 用于配置坐标系的参数，如轴映射、最大速度/加速度等。
-   `struct TCrdData`: 描述一个插补运动段的核心数据结构，包含了位置、速度、加速度、圆心、向量等所有插补所需信息。

### 5.2. 核心枚举 (在 `motion_enums.h` 中)

库广泛使用 `enum class` 来定义各种类型，关键枚举包括：

-   `ResourceType`: 资源类型（如限位、报警、编码器、轴等）。
-   `PrfMode`: 轴的规划模式（梯形、JOG、电子齿轮、插补等）。
-   `CrdCmdType`: 插补指令类型（直线、圆弧、螺旋线等）。
-   `AxisStatus`: 描述轴状态的位标志（使能、限位、原点、报警等），可进行位运算。
-   `MotionErrorCode`: 详细的运动控制错误码。

---

## 6. 高层封装 API (`MotionAPI`)

`motion_api.h` 中定义的 `MotionAPI` 类是推荐使用的主要接口。它将复杂的底层操作封装在易于使用的面向对象接口中。

### 6.1. 设计特点

-   **面向对象**: 所有功能都通过 `MotionAPI` 类的成员函数调用。
-   **RAII (资源获取即初始化)**: 对象的构造和析构函数管理着板卡的连接与断开，确保资源被正确处理。
-   **现代C++实践**:
    -   使用 `std::string`, `std::vector`, `std::array` 等容器简化数据管理。
    -   通过 `std::function` 实现异步回调，用于监控轴状态和处理错误。
-   **集成日志**: 内置 `logger`，方便调试。

### 6.2. 主要功能

#### 6.2.1. 连接管理

```cpp
// 构造一个API实例
MotionAPI api;
// 连接到控制器
MotionErrorCode code = api.openBoard("192.168.0.11", 8088);
// ...
// 断开连接 (在析构函数中自动调用)
api.closeBoard();
```

#### 6.2.2. 轴控制

```cpp
// 使能/失能轴
api.axisOn(0); // 使能0轴
api.axisOff(0); // 失能0轴

// JOG运动
api.setJogMode(0); // 为0号坐标系设置JOG模式
TJogPrm jogParams = { ... };
api.setJogPrm(0, jogParams);
api.jogUpdate(0, 1); // 0轴正向JOG
```

#### 6.2.3. 插补运动

```cpp
// 设置坐标系参数
TCrdPrm crdParams = { ... };
api.setCrdPrm(0, crdParams);

// 直线插补
// 在0号坐标系中，从当前点运动到(1000, 2000)，速度100，加速度500
api.ln(0, 1000, 2000, 100.0, 500.0, 0.0);

// 圆弧插补 (多种方式)
api.arcXYR(...); // 通过终点和半径
api.arcXYC(...); // 通过终点和圆心
```

#### 6.2.4. 状态监控与回调

```cpp
// 注册一个回调函数，当轴状态变化时被调用
api.registerAxisStatusCallback([](short axis, AxisStatus status) {
    // 处理状态变化...
});

// 注册一个错误处理回调
api.registerErrorCallback([](MotionErrorCode code, const std::string& msg) {
    // 处理错误...
});
```

---

## 7. 底层C风格API (`TADMotion`)

`ADMotionPackage.h` 定义了 `TADMotion` 类，它提供了更底层的、接近C语言风格的接口。此类中的函数大多是 `inline` 包装，实际调用了外部的C函数。

### 7.1. 设计特点

-   **协议层暴露**: 该接口的设计紧密围绕通信协议（如`@@`, `$$`等包头），处理原始二进制数据。
-   **C风格函数**: 接口函数类似于C语言，通常需要传递一个指向 `TADMotion` 实例的指针。
-   **手动数据管理**: 参数通常是C风格的数组（指针）和基本数据类型。

### 7.2. 主要功能

`TADMotion` 提供了与 `MotionAPI` 功能集类似但更底层的函数。

```cpp
TADMotion motion;
motion.OpenBoard("192.168.0.11", 8088);

motion.AxisOn(0); // 使能0轴

short axisMap[5] = {0, 1, -1, -1, -1};
// ... 其他C风格数组
motion.SetAxisPrm(0, axisMap, ...);

// 直线插补
motion.Ln(0, 1000, 2000, 100.0, 500.0);

motion.CloseBoard();
```

### 7.3. 通信数据处理

`ADMotionPackage.h` 还定义了用于网络通信和数据包解析的类和结构体，如 `TPackage_Motion` 和 `TADMotionConn`，以及用于处理原始数据流的回调。

## 8. 结论与建议

`ADMotion` 动态库提供了一套功能强大且设计灵活的接口。

-   对于**新的C++应用程序**，强烈建议使用 **`MotionAPI`**。它更安全、更易用，并且符合现代C++的设计哲学，能够显著提高开发效率和代码质量。
-   对于需要与**现有C代码库集成**或需要对**通信协议进行深度控制**的特殊场景，可以使用 **`TADMotion`** 提供的底层接口。

通过结合使用强类型枚举和清晰的数据结构，该库在实现复杂运动控制功能的同时，也保证了API的健壮性和可维护性。 