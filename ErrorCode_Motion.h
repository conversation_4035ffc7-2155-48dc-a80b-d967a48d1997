﻿/**
 * @file ErrorCode_Motion.h
 * @brief ADMotion运动控制库错误码处理头文件
 * @version 3.0 - 移除轮询函数，统一使用回调方式
 * @date 2024-12-19
 *
 * 注意：本版本已移除MotionGetErrorStr和Servo_GetErrString函数
 * 请使用g_Log回调机制获取错误信息，或直接调用GetMotionErrorString函数
 */

#pragma once
#include <string>

// 轮询函数已被移除，请使用以下方式获取错误信息：
// 1. 通过API_SetLogCallback设置回调函数，错误会自动通过g_Log记录
// 2. 直接调用GetMotionErrorString函数获取错误描述
//
// 示例：
// const wchar_t* errorDesc = GetMotionErrorString(errorCode);

// 如果需要兼容性支持，请包含motion_error_codes.h并使用GetMotionErrorString函数
