/**
 * @file error_handling_example.cpp
 * @brief ADMotion错误处理使用示例
 * @version 3.0
 * @date 2024-12-19
 * 
 * 本文件展示了新的统一错误处理方式的使用方法
 */

#include "admc_pci.h"
#include "motion_error_codes.h"
#include "logger_proxy.h"
#include <iostream>
#include <cstdio>

//=============================================================================
// 日志回调函数示例
//=============================================================================

/**
 * @brief 简单的控制台日志回调函数
 */
void ConsoleLogCallback(LogLevel level, const char* message, void* userData) {
    const char* levelStr = "";
    switch (level) {
        case LOG_DEBUG:   levelStr = "DEBUG"; break;
        case LOG_INFO:    levelStr = "INFO"; break;
        case LOG_WARNING: levelStr = "WARN"; break;
        case LOG_ERROR:   levelStr = "ERROR"; break;
        case LOG_FATAL:   levelStr = "FATAL"; break;
    }
    
    printf("[%s] %s\n", levelStr, message);
}

/**
 * @brief 文件日志回调函数示例
 */
void FileLogCallback(LogLevel level, const char* message, void* userData) {
    FILE* logFile = static_cast<FILE*>(userData);
    if (logFile) {
        const char* levelStr = "";
        switch (level) {
            case LOG_DEBUG:   levelStr = "DEBUG"; break;
            case LOG_INFO:    levelStr = "INFO"; break;
            case LOG_WARNING: levelStr = "WARN"; break;
            case LOG_ERROR:   levelStr = "ERROR"; break;
            case LOG_FATAL:   levelStr = "FATAL"; break;
        }
        
        // 获取当前时间
        time_t now = time(0);
        char timeStr[64];
        strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", localtime(&now));
        
        fprintf(logFile, "[%s] [%s] %s\n", timeStr, levelStr, message);
        fflush(logFile);
    }
}

//=============================================================================
// 使用示例
//=============================================================================

/**
 * @brief 演示新的错误处理方式
 */
void DemonstrateErrorHandling() {
    TADMotionConn* handle = nullptr;
    
    // 1. 打开连接
    short ret = API_OpenBoard(&handle, "192.168.2.2", 6666);
    if (ret != CMD_SUCCESS) {
        printf("打开板卡失败，错误码: %d\n", ret);
        return;
    }
    
    // 2. 设置控制台日志回调
    API_SetLogCallback(handle, ConsoleLogCallback, nullptr);
    API_SetLogLevel(handle, LOG_INFO);
    
    printf("=== 演示控制台日志回调 ===\n");
    
    // 3. 测试正常操作（会记录INFO日志）
    ret = MC_AxisEnable(handle, 0, 0, 1);
    if (ret == CMD_SUCCESS) {
        printf("轴使能成功\n");
    }
    
    // 4. 测试参数错误（会记录ERROR日志）
    ret = MC_AxisEnable(handle, 0, 99, 1);  // 无效轴号
    if (ret != CMD_SUCCESS) {
        printf("轴使能失败，错误码: %d\n", ret);
        
        // 直接获取错误描述
        const wchar_t* errorDesc = GetMotionErrorString(ret);
        wprintf(L"错误描述: %ls\n", errorDesc);
    }
    
    // 5. 演示文件日志
    printf("\n=== 演示文件日志回调 ===\n");
    FILE* logFile = fopen("motion_log.txt", "a");
    if (logFile) {
        API_SetLogCallback(handle, FileLogCallback, logFile);
        
        // 执行一些操作，日志会写入文件
        MC_AxisEnable(handle, 0, 1, 1);
        MC_Stop(handle, 1, 0);
        
        fclose(logFile);
        printf("日志已写入 motion_log.txt 文件\n");
    }
    
    // 6. 关闭连接
    API_CloseBoard(handle);
}

/**
 * @brief 演示错误码类型判断
 */
void DemonstrateErrorTypes() {
    printf("\n=== 错误码类型判断示例 ===\n");
    
    // 测试不同类型的错误码
    int testErrorCodes[] = {
        0,                              // 成功
        CMD_API_ERROR_OUT_RANGE,        // DLL内部错误
        TIMEOUT,                        // 通讯错误
        -1,                             // DSP错误
        0x0101                          // 伺服错误
    };
    
    for (int i = 0; i < 5; i++) {
        int errorCode = testErrorCodes[i];
        int errorType = GetErrorCodeType(errorCode);
        const wchar_t* errorDesc = GetMotionErrorString(errorCode);
        
        const char* typeStr = "";
        switch (errorType) {
            case 0: typeStr = "成功"; break;
            case 1: typeStr = "通讯错误"; break;
            case 2: typeStr = "DSP错误"; break;
            case 3: typeStr = "DLL内部错误"; break;
            case 4: typeStr = "伺服错误"; break;
            default: typeStr = "未知类型"; break;
        }
        
        printf("错误码: %d, 类型: %s, 描述: %ls\n", 
               errorCode, typeStr, errorDesc);
    }
}

/**
 * @brief 主函数
 */
int main() {
    printf("ADMotion 错误处理示例程序\n");
    printf("========================\n");
    
    // 演示错误处理
    DemonstrateErrorHandling();
    
    // 演示错误类型判断
    DemonstrateErrorTypes();
    
    printf("\n程序执行完成。\n");
    return 0;
}

//=============================================================================
// 迁移指南
//=============================================================================

/**
 * 从旧版本迁移到新版本的指南：
 * 
 * 旧代码：
 * ```cpp
 * short ret = MC_AxisEnable(handle, 0, 0, 1);
 * if (ret != CMD_SUCCESS) {
 *     std::wstring errorMsg = MotionGetErrorStr(ret);
 *     wcout << L"错误: " << errorMsg << endl;
 * }
 * ```
 * 
 * 新代码方式1（推荐 - 使用回调）：
 * ```cpp
 * // 设置一次回调函数
 * API_SetLogCallback(handle, MyLogCallback, nullptr);
 * 
 * // 错误会自动通过回调记录，无需手动处理
 * short ret = MC_AxisEnable(handle, 0, 0, 1);
 * if (ret != CMD_SUCCESS) {
 *     // 错误信息已通过回调记录，这里只需处理业务逻辑
 *     return ret;
 * }
 * ```
 * 
 * 新代码方式2（直接获取错误描述）：
 * ```cpp
 * short ret = MC_AxisEnable(handle, 0, 0, 1);
 * if (ret != CMD_SUCCESS) {
 *     const wchar_t* errorDesc = GetMotionErrorString(ret);
 *     wcout << L"错误: " << errorDesc << endl;
 * }
 * ```
 */
