/**
 * @file error_code_test.cpp
 * @brief ADMotion错误码重构验证测试程序
 * @version 2.0
 * @date 2024-12-19
 */

#include <iostream>
#include <iomanip>
#include <vector>
#include <string>
#include "motion_error_codes.h"
#include "ErrorCode_Motion.h"
#include "adconfig.h"

// 将宽字符串转换为UTF-8字符串用于控制台输出
std::string wstring_to_utf8(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    
    // 简化版本，实际项目中应使用更完善的转换
    std::string result;
    for (wchar_t wc : wstr) {
        if (wc < 128) {
            result += static_cast<char>(wc);
        } else {
            result += "?"; // 非ASCII字符用?替代
        }
    }
    return result;
}

/**
 * @brief 测试实际使用的错误码和错误信息的对应关系
 */
void testErrorCodeMapping() {
    std::cout << "\n=== 实际使用的错误码映射测试 ===" << std::endl;

    // 测试成功状态
    std::cout << "测试成功状态:" << std::endl;
    std::cout << "MOTION_SUCCESS (" << MOTION_SUCCESS << "): "
              << wstring_to_utf8(GetMotionErrorString(MOTION_SUCCESS)) << std::endl;

    // 测试通信错误
    std::cout << "\n测试通信错误:" << std::endl;
    std::vector<int> commErrors = {
        MOTION_ERROR_COMM_TIMEOUT,
        MOTION_ERROR_COMM_FORMAT,
        MOTION_ERROR_COMM_CONNECTION
    };

    for (int errorCode : commErrors) {
        std::cout << "错误码 " << errorCode << ": "
                  << wstring_to_utf8(GetMotionErrorString(errorCode)) << std::endl;
    }

    // 测试参数错误
    std::cout << "\n测试参数错误:" << std::endl;
    std::vector<int> paramErrors = {
        MOTION_ERROR_PARAM_OUT_RANGE,
        MOTION_ERROR_PARAM_TYPE,
        MOTION_ERROR_PARAM_POINTER,
        MOTION_ERROR_PARAM_INVALID,
        MOTION_ERROR_PARAM_HANDLE_INVALID
    };

    for (int errorCode : paramErrors) {
        std::cout << "错误码 " << errorCode << ": "
                  << wstring_to_utf8(GetMotionErrorString(errorCode)) << std::endl;
    }

    // 测试坐标系错误
    std::cout << "\n测试坐标系错误:" << std::endl;
    std::vector<int> crdErrors = {
        MOTION_ERROR_CRD_LINE_ZERO_LENGTH,
        MOTION_ERROR_CRD_ARC_CENTER,
        MOTION_ERROR_CRD_ARC_END_POSITION,
        MOTION_ERROR_CRD_ARC_RADIUS,
        MOTION_ERROR_CRD_ARC3D_COLLINEAR,
        MOTION_ERROR_CRD_ARC3D_RADIUS_SMALL
    };

    for (int errorCode : crdErrors) {
        std::cout << "错误码 " << errorCode << ": "
                  << wstring_to_utf8(GetMotionErrorString(errorCode)) << std::endl;
    }

    // 测试导出接口错误码
    std::cout << "\n测试导出接口错误码:" << std::endl;
    std::vector<int> admcErrors = {
        ADMC_SUCCESS,
        ADMC_ERROR_OPEN,
        ADMC_ERROR_OUT_RANGE,
        ADMC_ERROR_TYPE,
        ADMC_ERROR_CRD_BUF_FULL
    };

    for (int errorCode : admcErrors) {
        std::cout << "ADMC错误码 " << errorCode << ": "
                  << wstring_to_utf8(GetMotionErrorString(errorCode)) << std::endl;
    }

    // 测试新增的兼容性错误码
    std::cout << "\n测试新增的兼容性错误码:" << std::endl;
    std::vector<int> compatErrors = {
        CMD_API_ERROR_OPEN,
        CMD_API_ERROR_CRD_FIFO_FULL
    };

    for (int errorCode : compatErrors) {
        std::cout << "兼容性错误码 " << errorCode << ": "
                  << wstring_to_utf8(GetMotionErrorString(errorCode)) << std::endl;
    }
}

/**
 * @brief 测试兼容性映射
 */
void testCompatibilityMapping() {
    std::cout << "\n=== 兼容性映射测试 ===" << std::endl;
    
    // 测试旧错误码是否正确映射到新错误码
    std::cout << "测试旧错误码映射:" << std::endl;
    
    // CMD_SUCCESS应该映射到MOTION_SUCCESS
    std::cout << "CMD_SUCCESS (" << CMD_SUCCESS << ") -> MOTION_SUCCESS (" 
              << MOTION_SUCCESS << "): " << (CMD_SUCCESS == MOTION_SUCCESS ? "✓" : "✗") << std::endl;
    
    // CMD_API_ERROR_OUT_RANGE应该映射到MOTION_ERROR_PARAM_OUT_RANGE
    std::cout << "CMD_API_ERROR_OUT_RANGE (" << CMD_API_ERROR_OUT_RANGE 
              << ") -> MOTION_ERROR_PARAM_OUT_RANGE (" << MOTION_ERROR_PARAM_OUT_RANGE 
              << "): " << (CMD_API_ERROR_OUT_RANGE == MOTION_ERROR_PARAM_OUT_RANGE ? "✓" : "✗") << std::endl;
    
    // 测试更多映射
    std::cout << "CMD_API_ERROR_OPEN (" << CMD_API_ERROR_OPEN 
              << ") -> MOTION_ERROR_COMM_OPEN (" << MOTION_ERROR_COMM_OPEN 
              << "): " << (CMD_API_ERROR_OPEN == MOTION_ERROR_COMM_OPEN ? "✓" : "✗") << std::endl;
}

/**
 * @brief 测试兼容性函数
 */
void testCompatibilityFunctions() {
    std::cout << "\n=== 兼容性函数测试 ===" << std::endl;
    
    // 测试MotionGetErrorStr函数
    std::cout << "测试MotionGetErrorStr函数:" << std::endl;
    
    // 测试成功状态（特殊处理：旧版本中CMD_SUCCESS=1）
    std::wstring result1 = MotionGetErrorStr(1);
    std::cout << "MotionGetErrorStr(1): " << wstring_to_utf8(result1) << std::endl;
    
    // 测试其他错误码
    std::wstring result2 = MotionGetErrorStr(MOTION_ERROR_PARAM_OUT_RANGE);
    std::cout << "MotionGetErrorStr(" << MOTION_ERROR_PARAM_OUT_RANGE << "): " 
              << wstring_to_utf8(result2) << std::endl;
    
    // 测试GetErrorStr函数
    std::cout << "\n测试GetErrorStr函数:" << std::endl;
    const wchar_t* result3 = GetErrorStr(MOTION_ERROR_COMM_TIMEOUT);
    std::cout << "GetErrorStr(" << MOTION_ERROR_COMM_TIMEOUT << "): " 
              << wstring_to_utf8(result3) << std::endl;
}

/**
 * @brief 测试伺服错误码
 */
void testServoErrorCodes() {
    std::cout << "\n=== 伺服错误码测试 ===" << std::endl;

    std::vector<int> servoErrors = {
        0x0101, 0x0102, 0x0200, 0x0300
    };

    for (int errorCode : servoErrors) {
        std::cout << "伺服错误码 0x" << std::hex << errorCode << std::dec << ": "
                  << wstring_to_utf8(GetMotionErrorString(errorCode)) << std::endl;
    }

    // 测试Servo_GetErrString函数
    std::cout << "\n测试Servo_GetErrString函数:" << std::endl;
    std::wstring servoResult = Servo_GetErrString(0x0101);
    std::cout << "Servo_GetErrString(0x0101): " << wstring_to_utf8(servoResult) << std::endl;
}

/**
 * @brief 测试未知错误码处理
 */
void testUnknownErrorCodes() {
    std::cout << "\n=== 未知错误码测试 ===" << std::endl;
    
    // 测试不存在的错误码
    int unknownError = 9999;
    const wchar_t* result = GetMotionErrorString(unknownError);
    std::cout << "未知错误码 " << unknownError << ": " 
              << wstring_to_utf8(result) << std::endl;
}

/**
 * @brief 主测试函数
 */
int main() {
    std::cout << "=== ADMotion错误码重构验证测试 ===" << std::endl;
    std::cout << "测试新的统一错误码体系是否正常工作..." << std::endl;
    
    try {
        // 执行各项测试
        testErrorCodeMapping();
        testCompatibilityMapping();
        testCompatibilityFunctions();
        testServoErrorCodes();
        testUnknownErrorCodes();
        
        std::cout << "\n=== 测试完成 ===" << std::endl;
        std::cout << "所有测试已执行完毕。请检查输出结果确认错误码体系工作正常。" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
