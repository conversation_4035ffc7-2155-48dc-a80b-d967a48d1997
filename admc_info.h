﻿#pragma once
#ifndef ADMC_INFO_H
#define ADMC_INFO_H

#include "adconfig.h"
#include "ADOSType.h"
#include "motion_types.h"
#include "motion_enums.h"

// 版本类型定义
#define VERSION_DLL  0
#define VERSION_DSP  1
#define VERSION_FPGA 2

// 为了向后兼容性保留的宏定义，新代码应使用ResourceType枚举类
#define RES_NONE (-1)
#define RES_LIMIT_POSITIVE 0
#define RES_LIMIT_NEGATIVE 1
#define RES_ALARM          2
#define RES_HOME           3
#define RES_GPI            4
#define RES_ARRIVE         5
#define RES_MPG            6
#define RES_ENABLE 10
#define RES_CLEAR  11
#define RES_GPO    12
#define RES_DAC     20
#define RES_STEP    21
#define RES_PULSE   22
#define RES_ENCODER 23
#define RES_ADC     24
#define RES_AXIS    30
#define RES_PROFILE 31
#define RES_CONTROL 32
#define RES_CRD     33
#define RES_COMPARE 34

// 为了向后兼容性保留的宏定义，新代码应使用布尔值
#define FALSE 0
#define TRUE  1
#endif


//测试提交