#include "motion_api.h"
#include "ErrorCode_Motion.h"
#include <chrono>
#include <thread>
#include <sstream>

// 获取错误码对应的错误类别
ErrorCategory getErrorCategory(MotionErrorCode code) {
    short codeValue = static_cast<short>(code);

    if (codeValue == 1) {
        return ErrorCategory::None;
    }

    if (codeValue >= 100 && codeValue < 120) {
        return ErrorCategory::Parameter;
    }

    switch (code) {
        case MotionErrorCode::ApiErrorOpen:
            return ErrorCategory::Communication;

        case MotionErrorCode::CrdNumError:
        case MotionErrorCode::AxisNumError:
        case MotionErrorCode::AxisDirError:
        case MotionErrorCode::AxisMapError:
        case MotionErrorCode::AxisStsError:
            return ErrorCategory::Parameter;

        case MotionErrorCode::AxisEnableError:
        case MotionErrorCode::AxisDisenableError:
        case MotionErrorCode::AxisEnableStateError:
            return ErrorCategory::Axis;

        case MotionErrorCode::AxisPositiveLimError:
        case MotionErrorCode::AxisNegativeLimError:
        case MotionErrorCode::AxisRunError:
        case MotionErrorCode::PrfRunError:
        case MotionErrorCode::MotionTypeError:
        case MotionErrorCode::CirclePlantError:
        case MotionErrorCode::AxisAlarm:
            return ErrorCategory::Motion;

        default:
            return ErrorCategory::Unknown;
    }
}

// 获取系统状态对应的描述
std::string getSystemStatusDescription(SystemStatus status) {
    switch (status) {
        case SystemStatus::Disconnected:
            return "未连接";
        case SystemStatus::Connecting:
            return "连接中";
        case SystemStatus::Connected:
            return "已连接";
        case SystemStatus::Disconnecting:
            return "断开连接中";
        case SystemStatus::Initializing:
            return "初始化中";
        case SystemStatus::Ready:
            return "就绪";
        case SystemStatus::Running:
            return "运行中";
        case SystemStatus::Error:
            return "错误";
        case SystemStatus::Stopping:
            return "停止中";
        case SystemStatus::Stopped:
            return "已停止";
        default:
            return "未知状态";
    }
}

// 获取系统状态
SystemStatus MotionAPI::getSystemStatus() const {
    return m_systemStatus;
}

// 注册系统状态变化回调函数
int MotionAPI::registerStatusCallback(std::function<void(SystemStatus)> callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    int id = m_nextCallbackId++;
    m_statusCallbacks[id] = callback;
    return id;
}

// 取消注册系统状态变化回调函数
bool MotionAPI::unregisterStatusCallback(int callbackId) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    auto it = m_statusCallbacks.find(callbackId);
    if (it != m_statusCallbacks.end()) {
        m_statusCallbacks.erase(it);
        return true;
    }
    return false;
}

// 获取系统诊断信息
std::map<std::string, std::string> MotionAPI::getDiagnosticInfo() const {
    std::map<std::string, std::string> info;

    // 添加基本信息
    info["系统状态"] = getSystemStatusDescription(m_systemStatus);
    info["连接状态"] = m_conn.isValid() ? "已连接" : "未连接";

    // 如果已连接，添加更多信息
    if (m_conn.isValid()) {
        // 获取FPGA版本
        auto fpgaVersion = getFpgaVersion();
        if (fpgaVersion.hasValue()) {
            info["FPGA版本"] = std::to_string(fpgaVersion.value());
        }

        // 获取设备输入输出状态
        auto input = getDeviceInput();
        if (input.hasValue()) {
            std::stringstream ss;
            ss << "0x" << std::hex << input.value();
            info["设备输入"] = ss.str();
        }
    }

    return info;
}

// 检查系统是否处于错误状态
bool MotionAPI::isSystemInError() const {
    return m_systemStatus == SystemStatus::Error;
}

// 清除系统错误
MotionErrorCode MotionAPI::clearSystemError() {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    // 重置系统状态
    updateSystemStatus(SystemStatus::Ready);
    m_lastErrorDetails = "";

    // 清除所有轴的报警
    for (short axis = 0; axis < 8; ++axis) {
        MotionErrorCode result = axisClearAlarm(axis);
        if (result != MotionErrorCode::Success) {
            logError(result, "清除轴" + std::to_string(axis) + "报警");
        }
    }

    return MotionErrorCode::Success;
}

// 获取最后一次错误的详细信息
std::string MotionAPI::getLastErrorDetails() const {
    return m_lastErrorDetails;
}

// 注册轴状态变化回调函数
int MotionAPI::registerAxisStatusCallback(AxisStatusCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    int id = m_nextCallbackId++;
    m_axisStatusCallbacks[id] = callback;
    return id;
}

// 取消注册轴状态变化回调函数
bool MotionAPI::unregisterAxisStatusCallback(int callbackId) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    auto it = m_axisStatusCallbacks.find(callbackId);
    if (it != m_axisStatusCallbacks.end()) {
        m_axisStatusCallbacks.erase(it);
        return true;
    }
    return false;
}

// 开始轴状态监控
MotionErrorCode MotionAPI::startAxisMonitoring(short axis, int intervalMs) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    // 这里应该启动一个线程来定期监控轴状态
    // 由于这只是一个示例，我们不会实际启动线程
    m_conn.log(LOG_INFO, "开始监控轴%d状态，间隔%d毫秒", axis, intervalMs);

    return MotionErrorCode::Success;
}

// 停止轴状态监控
MotionErrorCode MotionAPI::stopAxisMonitoring(short axis) {
    if (!m_conn.isValid()) {
        return MotionErrorCode::ApiErrorOpen;
    }

    // 这里应该停止监控线程
    m_conn.log(LOG_INFO, "停止监控轴%d状态", axis);

    return MotionErrorCode::Success;
}

// 检查轴是否处于特定状态
bool MotionAPI::isAxisInStatus(short axis, AxisStatus status) const {
    if (!m_conn.isValid()) {
        return false;
    }

    auto axisStatus = getAxisStatus(axis);
    if (!axisStatus.hasValue()) {
        return false;
    }

    return (static_cast<uint16_t>(axisStatus.value() & status) != 0);
}

// 等待轴达到特定状态
bool MotionAPI::waitForAxisStatus(short axis, AxisStatus status, int timeoutMs) {
    if (!m_conn.isValid()) {
        return false;
    }

    auto startTime = std::chrono::steady_clock::now();

    while (true) {
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime).count();

        if (elapsedMs > timeoutMs) {
            m_conn.log(LOG_WARNING, "等待轴%d状态超时", axis);
            return false;
        }

        if (isAxisInStatus(axis, status)) {
            return true;
        }

        // 休眠一小段时间，避免过度占用CPU
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}
