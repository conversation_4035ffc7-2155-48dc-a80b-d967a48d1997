#ifndef ADMOTIONPACKAGE_H
#define ADMOTIONPACKAGE_H

#include <string>
#include <functional>
#include <atomic>
#include "asiosocket.h"
#include "sysSemaphore.h"
#include "admc_pci.h"
#include "motion_types.h"
#include "motion_enums.h"

// 前向声明
struct TCardData;

/*安达4轴运控数据接口：
 格式： 数据头2字节+二进制数据+数据尾2字节
 1、数据头有3种：
   1） @@ ,运控回复上位机的指令
   2） $$ ,运控主动上传IO，含轴IO及轴状态
   3） %% ,运控主动上传各轴坐标，
   4)  ##,运控主动上传PDO数据
 2、数据尾总是 !!
 3、数据：
    1） 数据头@@ 运控回复上位机的指令 + TPci变长数据（注该部分数据没有转发到LMES）
        typedef struct
        {
            unsigned short flag;      //未用
            unsigned short ArrayCount;//data Array Count
            unsigned short checksum;  //未用
            unsigned short tick;      //指令标识，发送的tick与返回的tick要一致
            short          data[COMMAND_DATA_LEN];//data[0] = 命令CMD , 实际数据从data[1]开始
        } TPci;
       变长数据字节数 datalen = 4*sizeof(short) + (gPci.dataLen)*sizeof(short)

    2） 数据头$$ IO状态自动发送  datalen = sizeof(TCardIO),定长16字节二进制数据（小端）
    typedef struct
    {
        volatile unsigned short   X1State; //轴0的轴IO与状态
        volatile unsigned short   Y1State; //轴1的轴IO与状态
        volatile unsigned short   X2State; //轴2的轴IO与状态
        volatile unsigned short   Y2State; //轴3的轴IO与状态
        。。。
        volatile uint32_t Input;   //IO：32个输入口状态 不足32补0
        。。。。
        volatile uint32_t Output;  //IO：32个输出口状态 不足32补0
        。。。。
    } TCardIO;

        //输入输出IO,每位代表一个IO状态开关，由低向高排列，共32个IO，Input_1 = var & 0x01 ;
        //轴IO状态：
        ENA Bit 0  使能	    电机上电
        PEL Bit 1  正限位信号	正向限位感应器
        NEL Bit 2  负限位信号	负向限位感应器
        ORG Bit 3  原点信号	原点感应器
        INP Bit 4  到位信号	电机移动到位
        MOV Bit 5  移动信号	电机移动中
        ALM Bit 6  报警信号	电机报错 //报警后用API取错误码，通过错误码得到报警信息
        EMG Bit 7  急停信号	急停信号
        RST Bit 8  复位中
        ANG Bit 9  角度识别
        ANG Bit 10 高压信号
        ANG Bit 11 高压使能

    3） 数据头%% 位置自动发送 datalen = sizeof(TCardAxisPos) 定长16字节二进制数据（4轴旧版本）
       //新版本多两字节 轴数量 轴坐标数据类型
        typedef struct
        {
            volatile int X1Pos; //轴0的当前位置
            volatile int Y1Pos; //轴1的当前位置
            volatile int X2Pos; //轴2的当前位置
            volatile int Y2Pos; //轴3的当前位置
            。。。
        } TCardAxisPos;
    4）PDO 数据 ，以##开头+12 字节 PDO 数据+ !! 结束
        PDO 数据 12 字节 1 组
    - Node_id 4 字节（含 Frame 号）
    - 8 字节数据 （各站点数据依协定格式解释）
*/
//解码后的PDO数据：
// 使用motion_enums.h中定义的ADMotionDataType枚举类
// 为了保持向后兼容性，保留旧的枚举定义
enum ADMotionDataTYPE
{
    ADMC_CMD = 0,// @@
    ADMC_IO = 1,// $$
    ADMC_POS = 2,// %%
    ADMC_PDO = 3,// ##
};

// 转换函数，将ADMotionDataTYPE转换为ADMotionDataType
inline ADMotionDataType ConvertToADMotionDataType(ADMotionDataTYPE type) {
    switch(type) {
        case ADMC_CMD: return ADMotionDataType::ADMC_CMD;
        case ADMC_IO: return ADMotionDataType::ADMC_IO;
        case ADMC_POS: return ADMotionDataType::ADMC_POS;
        case ADMC_PDO: return ADMotionDataType::ADMC_PDO;
        default: return ADMotionDataType::ADMC_CMD;
    }
}

struct HEAD_Motion
{
    ADMotionDataType type;    // 使用新的枚举类型 ADMotionDataType
    int datalen; //数据长度 type = ADMotionDataType::ADMC_IO，datalen = sizeof(TCardIO);
                 //type = ADMotionDataType::ADMC_POS 时 datalen = sizeof(TCardAxisPos);
    //当 type == ADMotionDataType::ADMC_CMD 时,有以下数据
    unsigned short flag;//保留
    unsigned short ArrayCount;//short数组个数
    unsigned short checksum;//保留
    unsigned short tick;//Command索引
    //IO
    unsigned char StartIndex_IOData;//数据开始
    unsigned char AxisIO_num;//轴数量
    unsigned char IO_inputNum;//I口组数量  每组16个
    unsigned char IO_outputNum;//O口组数量 每组16个
    unsigned char IOChange;//IO状态变更： bit0:AxisIO bit1:IO_input bit2:IO_output
    //轴坐标
    unsigned char StartIndex_AxisPos;//数据开始
    unsigned char Axis_num;//轴数量
    unsigned char AxisPosType;//轴坐标数据类型  默认0=32 位整数  1=64 位整数 2=32 位浮点数 3=64 位浮点数
};

#ifndef DEF_TCardIO
#define DEF_TCardIO
typedef struct
{
    volatile uint16_t positionCrd[4][2];
    volatile uint16_t Input[4];
    volatile uint16_t Output[4];  //31->26
} TCardIO;
#endif

typedef std::function< void(TADMotionConn *,HEAD_Motion const &,const std::string &data,void *Tag) > Event_MCDataRecieved;
typedef std::function< void(TADMotionConn *,HEAD_Motion const &,TCardIO DataOld,TCardIO DataNew,void *Tag) > Event_MCIOChange;

//数据包定义
class TPackage_Motion
{
public:
    int         StartCheckSize = 8;
    int         HeadSize = 2; //包头大小 @@ $$ %% ##
    int         EndSize  = 2; //包尾大小 !!
    void*       TagCall = nullptr;
    std::string ipstr;
    std::string Title = "Motion";

    HEAD_Motion Head;//包头结构，一定要有数据区定义：datalen

    typedef std::function< void(HEAD_Motion const& Head, std::string& data, void* CAllTag) > Callback_MotionRead;

    //查找并解释一个完整的包头（不含数据），成功返回包头开始索引，失败返回-1
    int FindHead(const unsigned char* buff, int len, HEAD_Motion& Head);
    //确认包尾字符是否正确。
    int IsEndChar(const unsigned char* pEnd) { return (pEnd[0] == '!' && pEnd[1] == '!') ? 0 : 0xffff; }

    Callback_MotionRead pOnReadData = ReadPackage; // nullptr; // ReadPackage;

    static void ReadPackage(HEAD_Motion const& Head, std::string& data, void* CAllTag);
};
class TADMotionConn
{
    bool *f_pConnected  = nullptr;
    bool *f_pConnecting = nullptr;
public:
    TADMotionConn();
    ~TADMotionConn();
    //连接LineMes
    bool Connection(std::string const& ipaddress, int port);
    void Disconnect();
    void SetConnectVar(bool *pConnected,bool *pConnecting)
    {
        f_pConnected = pConnected ;
        f_pConnecting = pConnecting ;
    }
    //关闭连接
    void             Close();
    void             SendCloseMsg();
    bool             ReadConnectState();
    bool             isOpen() { return m_socket && m_socket->isConnected(); }
    int              write(const char* data, int dataLen) { return m_socket->write(data, dataLen); }
    unsigned __int64 GetTick_UpdategCardIO();
    //连接状态
    bool Connected  = false;
    bool Connecting = false;
    //超时 ms
    int TimeOut = 5000;

    std::string m_ip;           // ip
    int         m_port = 10006; //端口
    int CardNo ;//卡号

    TCardData* CardData;

    void setCallback(Event_MCDataRecieved callback) {callback_data = callback;}
    inline void SetCallbackEvent(Callback_SocketEvent pCallback, void* callTag)
    {
        m_socket->SetCallbackEvent(pCallback,callTag);
    }

    Event_MCDataRecieved   callback_data  = nullptr;
    Event_MCIOChange OnIOChange = nullptr;
    void *Event_Tag = nullptr;
private:
    //通讯socket
    TAsioSocketBase* m_socket = nullptr;

    TNetPackageReader< TPackage_Motion > PackageReader;
};

class TADMotion : public TADMotionConn
{
public:
    inline short OpenBoard(const char* ip, int port) { return API_OpenBoard(this, ip, port); } //打开卡
    inline short CloseBoard() { return API_CloseBoard(this); }                                 //关闭卡
    inline short ResetBoard() { return API_ResetBoard(this); }                                 //复位卡

    inline short SetAxisPrm(
        short crd, short* AxisMap, short* AxisDir, int32_t* VelMax, int32_t* AccMax, int32_t* Positive, int32_t* Negative)
    { //设置轴参数
        return API_SetAxisPrm(this, crd, AxisMap, AxisDir, VelMax, AccMax, Positive, Negative);
    }
    inline short AxisOn(short axis) { return API_AxisOn(this, axis); }   //使能开
    inline short AxisOff(short axis) { return API_AxisOff(this, axis); } //使能关

    inline short SetJogMode(short crd) { return API_SetJogMode(this, crd); }                         //设置jog模式
    //inline short SetJogPrm(short crd, TJogPrm* pPrm) { return API_SetJogPrm(this, crd, pPrm); }      //设置jog参数
    inline short New_JogUpdate(short axis, short dir) { return API_JogUpdate(this, axis, dir); } //更新Y-

    // Crd and Axis Trap
    inline short SetCrdTrapMode(short crd) { return API_SetCrdTrapMode(this, crd); }                     //设置点位模式
    //inline short SetCrdTrapPrm(short crd, TTrapPrm* pPrm) { return API_SetCrdTrapPrm(this, crd, pPrm); } //设置点位参数
    inline short CrdTrapUpdate(short crd) { return API_CrdTrapUpdate(this, crd); }                       //更新

    inline short SetAxisTrapMode(short crd, short axis) { return API_SetAxisTrapMode(this, axis); } //设置点位模式
    inline short SetAxisTrapPrm(short axis, double IncrPos, double velMax, double acc, short rat)
    {
        return API_SetAxisTrapPrm(this,axis, IncrPos, velMax, acc, rat);
    }                                                                                //设置点位参数
    inline short AxisTrapUpdate(short crd) { return API_AxisTrapUpdate(this, crd); } //


    inline short GoHome(short crd) { return API_GoHome(this, crd); }
    inline short AxisGoHome(short axis) { return API_AxisGoHome(this, axis); }
    inline short AxisClearAlarm(short axis) { return API_AxisClearAlarm(this, axis); }
    //坐标系
    inline short SetCrdPrm(short crd, double synVelMax, double synAccMax) { return API_SetCrdPrm(this, crd, synVelMax, synAccMax); }

    inline short CrdStart(short crd) { return API_CrdStart(this, crd); } //坐标插补开始
    inline  short CrdStop(short mask, short option, short stopType); //坐标插补停止
    inline  short CrdPause(short crd); //坐标插补暂停

    //直线圆弧指令
    inline short Ln(short crd, int32_t x, int32_t y, double synVel, double synAcc)
    {
        return API_Ln(this, crd, x, y, synVel, synAcc);
    }

    inline short ArcXY_3point(short    crd,
                              int32_t* p1,
                              int32_t* p2,
                              int32_t* p3,
                              double   synVel,
                              double   synAcc,
                              double   velEnd)
    {
        return API_ArcXY_3point(this, crd, p1, p2, p3, synVel, synAcc);
    }
    inline short
        ArcXYR(short crd, int32_t x, int32_t y, double radius, short circleDir, double synVel, double synAcc)
    {
        return API_ArcXYR(this, crd, x, y, radius, circleDir, synVel, synAcc);
    }
    inline short ArcXYC(short   crd,
                        int32_t x,
                        int32_t y,
                        double  xCenter,
                        double  yCenter,
                        short   circleDir,
                        double  synVel,
                        double  synAcc,
                        double  velEnd)
    {
        return API_ArcXYC(this, crd, x, y, xCenter, yCenter, circleDir, synVel, synAcc);
    }
    inline short InitLookAhead(short crd, double accMax, short count) { return API_InitLookAhead(this, crd, accMax, count); }
    inline short CloseLookAhead(short crd) { return API_CloseLookAhead(this, crd); }

    inline short SetDeviceOutput(int* deviceOutput) { return API_SetDeviceOutput(this, deviceOutput); }
    inline short GetDeviceInput(int32_t* deviceInput) { return API_GetDeviceInput(this, deviceInput); }


    inline short GetAixsPos(short axis, double& pPos) { return API_GetAixsPos(this, axis, pPos); }
    inline short GetCrdPos(short crd, double* pPos) { return API_GetCrdPos(this, crd, pPos); }

    inline short GetAxisStatus(short axis, short& Sts) { return API_GetAxisStatus(this, axis, Sts); }

    inline short DebugModelOption(bool bEnable) { return API_DebugModelOption(this, bEnable); }

    inline short PosCmpEnable(short crd, bool bEnable, short error, short frontTime)
    {
        return API_PosCmpEnable(this, crd,bEnable, error,frontTime);
    } //// 是否开启位置比较功能
    inline short SetPosCmpPoint(short crd, short seg, double pnt[10240][2], int nPntSize)
    {
        return API_SetPosCmpPoint(this, crd, seg, pnt, nPntSize);
    }                                                                              ////  传输要参与位置比较的点位
    inline short ClearCmpPoint(short crd) { return API_ClearCmpPoint(this, crd); } ////    清理点位
    inline short SetPosCmpOutp(short crd,short  outNum, short  outtype,long  hlTime_ms,long  duty_ms)
    {
        return API_SetPosCmpOutp(this, crd, outNum,outtype, hlTime_ms,duty_ms);
    } ////  设置输出的端口，输出方式，1为pulse，2为level



    //直接命令操作高速IO电平：
    //电平模式支持： io_num ： IO端口 0/1, 返回值 = 0 ，代表运控已成功传输命令到DSP,
    //电平模式有效
     short  SetIOPluseEnable(short crd, short io_num, short IO_Enable)
     { return API_SetIOPluseEnable(this,crd,  io_num,  IO_Enable);}
    //直接输出高或低电平。
     short  SetIOPluseState(short crd, short io_num, short IO_State)
        { return API_SetIOPluseState(this,crd,  io_num,  IO_State);}
    //直接翻转电平
     short  SetIOPluseTrigger(short crd, short io_num, short IO_Trigger)
        { return API_SetIOPluseTrigger(this,crd,  io_num,  IO_Trigger);}
    //取FPGA版本，version = 260 ，该函数成功上面三个函数才有效。否则无效。
     short  GetFpgaVersion(short &version)
        { return API_GetFpgaVersion(this,version);}
    //--------------
     short SendSpeedIO_Point(short crd, short pointNum, SpeedIOPoint p[])
     {return API_SendSpeedIO_Point(this, crd, pointNum, p);}
     short SpeedIO_Enable(short crd, short enable, short IO_num)
     {return API_SpeedIO_Enable(this, crd, enable, IO_num);}
     short SpeedIO_ClearPoint(short crd)
     {return API_SpeedIO_ClearPoint(this, crd);}
};


#endif // ADMOTIONPACKAGE_H

