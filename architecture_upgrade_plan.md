# 架构升级方案：引入事件驱动回调以优化状态获取

## 1. 摘要 (Executive Summary)

当前 `ADMotion` 库的状态获取机制（如 `API_GetCrdPos`）依赖于客户端进行主动轮询（Polling）。此方案旨在通过引入**事件驱动的回调（Callback）机制**，从根本上解决轮询模式带来的CPU资源浪费、实时性差和客户端实现复杂等问题。

我们将设计并实现一套新的API，允许客户端注册回调函数。当坐标系位置、轴状态等关键数据在库内部发生变化时，将由库**主动推送（Push）**这些更新给客户端，从而实现一个更高效、实时和易用的系统。

## 2. 问题分析 (Problem Statement)

目前，客户端为了获取实时的坐标位置，必须实现一个定时器，并以固定频率（如每20ms）调用 `API_GetCrdPos`。这种轮询模式存在以下核心缺陷：

-   **资源浪费**: 即使位置没有变化，轮询调用依旧会发生，消耗不必要的CPU周期。
-   **实时性差**: 数据更新存在固有延迟，延迟时间取决于轮询间隔。
-   **实现复杂**: 每个客户端都需要重复实现一套线程和定时器逻辑。
-   **调优困难**: 难以找到一个在性能开销和数据实时性之间达到完美平衡的轮询频率。

## 3. 解决方案：事件驱动的回调模型

我们将借鉴已成功用于日志系统的回调模型，为关键状态（如位置和轴状态）的更新引入专门的回调机制。

### 3.1 方案核心

1.  **定义新的回调类型**: 为不同的状态更新（如位置更新、轴状态更新）定义专门的、类型安全的回调函数指针。
2.  **提供注册/注销API**: 暴露新的C API函数，允许客户端为特定的设备句柄（`handle`）注册或注销这些回调。
3.  **内部实现推送逻辑**: 在DLL内部的数据接收/处理线程中，当检测到状态变化时，立即查找并调用已注册的回调函数，将最新的数据推送给客户端。

### 3.2 方案优势

-   **高效**: 仅在数据实际变化时才执行客户端代码，无任何空轮询。
-   **实时**: 数据从硬件到达DLL后几乎能零延迟地通知到客户端。
-   **简单**: 客户端代码极大简化，无需再关心线程和定时器管理。
-   **专业**: 将复杂的底层数据处理逻辑封装在库内部，为上层提供清晰、现代的API。

## 4. 代码实现指南 (Implementation Details)

### 步骤1：在 `include/admc_pci.h` 中定义新API

我们需要在头文件中添加新的回调函数类型定义和API函数声明。

```c
// [ additions to admc_pci.h ]

/******************************************* 状态更新回调 *******************************************/

/**
 * @brief 位置更新回调函数指针类型
 *
 * 当控制卡的坐标系位置发生变化时，此回调函数将被调用。
 * @warning 此回调函数在DLL的内部数据接收线程中被调用，而非主线程。
 *          请勿在此函数中执行耗时操作或直接更新UI。UI更新应通过消息机制投递到主线程处理。
 *
 * @param crd 产生位置更新的坐标系编号。
 * @param pPos 一个包含新位置的数组 (例如: [x, y])，其内容在回调返回后可能失效。
 * @param userData 用户在注册回调时提供的自定义数据指针。
 */
typedef void(__stdcall* PositionUpdateCallback)(short crd, const double* pPos, void* userData);

/**
 * @brief 轴状态更新回调函数指针类型
 *
 * 当轴的状态（如报警、使能、限位触发等）发生变化时，此回调将被调用。
 *
 * @param axis 发生状态变化的轴编号。
 * @param newStatus 新的轴状态码。
 * @param userData 用户在注册回调时提供的自定义数据指针。
 */
typedef void(__stdcall* AxisStatusCallback)(short axis, int newStatus, void* userData);


/**
 * @brief 为指定的运动控制句柄设置位置更新回调函数。
 * 
 * @param handle 运动控制句柄。
 * @param callback 用户实现的位置更新回调函数。如果传入NULL，则禁用该句柄的位置更新回调。
 * @param userData 将在每次调用回调时原样传回给用户的自定义数据。
 * @return 错误码。
 */
MC_API short __stdcall API_SetPositionUpdateCallback(TADMotionConn* handle, PositionUpdateCallback callback, void* userData);

/**
 * @brief 为指定的运动控制句柄设置轴状态更新回调函数。
 * 
 * @param handle 运动控制句柄。
 * @param callback 用户实现的轴状态更新回调函数。如果传入NULL，则禁用该句柄的状态回调。
 * @param userData 将在每次调用回调时原样传回给用户的自定义数据。
 * @return 错误码。
 */
MC_API short __stdcall API_SetAxisStatusCallback(TADMotionConn* handle, AxisStatusCallback callback, void* userData);

```

### 步骤2：在 `motion_conn.h` / `motion_conn.cpp` 中扩展 `MotionConn`

`MotionConn` C++包装类需要存储这些新的回调指针和用户数据。

```cpp
// [ additions to motion_conn.h ]

class MotionConn {
public:
    // ... 现有成员 ...

    // 新增设置回调的方法
    void setPositionUpdateCallback(PositionUpdateCallback callback, void* userData);
    void setAxisStatusCallback(AxisStatusCallback callback, void* userData);

    // 内部数据处理线程会调用这些方法来触发回调
    void triggerPositionUpdate(short crd, const double* pPos);
    void triggerAxisStatusUpdate(short axis, int newStatus);

private:
    // ... 现有成员 ...
    
    // 新增用于存储回调信息的成员变量
    PositionUpdateCallback m_posCallback;
    void* m_posUserData;

    AxisStatusCallback m_axisStatusCallback;
    void* m_axisStatusUserData;

    std::mutex m_callbackMutex; // 用于保护回调调用的互斥锁
};
```

### 步骤3：在 `motion_api.cpp` 中实现新的C API

这些C API函数将作为桥梁，调用`MotionConn`实例的相应方法。

```cpp
// [ additions to motion_api.cpp ]

short __stdcall API_SetPositionUpdateCallback(TADMotionConn* handle, PositionUpdateCallback callback, void* userData) {
    if (!handle) return -1; // 或其他错误码
    handle->conn->setPositionUpdateCallback(callback, userData);
    return 0;
}

short __stdcall API_SetAxisStatusCallback(TADMotionConn* handle, AxisStatusCallback callback, void* userData) {
    if (!handle) return -1;
    handle->conn->setAxisStatusCallback(callback, userData);
    return 0;
}
```

### 步骤4：在核心数据接收线程中触发回调

这是最关键的一步。在DLL内部负责与硬件通信并接收数据的后台线程中，当解析到一个新的数据包时：

```cpp
// [ 伪代码：在数据接收线程的循环中 ]

while (running) {
    // 从socket或硬件驱动接收数据包
    auto dataPacket = receiveData();

    // 解析数据包
    if (isPositionData(dataPacket)) {
        short crd = parseCrd(dataPacket);
        double newPos[2] = {parsePosX(dataPacket), parsePosY(dataPacket)};
        
        // 找到对应的MotionConn实例
        MotionConn* conn = findConnForThisData(); 
        
        // 触发位置更新回调
        if (conn) {
            conn->triggerPositionUpdate(crd, newPos);
        }

    } else if (isAxisStatusData(dataPacket)) {
        short axis = parseAxis(dataPacket);
        int status = parseStatus(dataPacket);

        MotionConn* conn = findConnForThisData();
        if (conn) {
            conn->triggerAxisStatusUpdate(axis, status);
        }
    }
    // ... 处理其他数据类型
}
```

## 5. 客户端使用示例 (Usage Example)

客户端代码将变得异常简洁和直观。

```cpp
#include "admc_pci.h"
#include <iostream>
#include <string>
#include <windows.h> // 仅用于示例中的PostMessage

// -- 全局或类成员的回调函数 --

// 位置更新回调
void __stdcall myPositionCallback(short crd, const double* pPos, void* userData) {
    // userData可以用来传递任何东西，比如一个指向UI窗口的HWND
    HWND hLogWnd = static_cast<HWND>(userData);

    char buffer[256];
    sprintf_s(buffer, "Position Update - CRD %d: X=%.3f, Y=%.3f\n", crd, pPos[0], pPos[1]);

    // 重要：不要直接在这里更新UI！
    // 使用PostMessage或等效机制将数据发送到主UI线程
    if (hLogWnd) {
        // 假设WM_LOG_MESSAGE是自定义的Windows消息
        // PostMessage是线程安全的
        PostMessage(hLogWnd, WM_LOG_MESSAGE, 0, (LPARAM)_strdup(buffer));
    } else {
        std::cout << buffer;
    }
}

// 轴状态更新回调
void __stdcall myAxisStatusCallback(short axis, int newStatus, void* userData) {
    std::cout << "Status Update - Axis " << axis << ": New Status=" << newStatus << std::endl;
}


// -- 主程序逻辑 --

int main() {
    TADMotionConn* handle = API_CreateBoard();
    if (!handle) return -1;

    // 假设 hMyLogWindow 是一个文本框或列表框的窗口句柄
    HWND hMyLogWindow = find_my_log_window(); 

    // 注册回调
    // 将UI控件的句柄作为userData传递，以便在回调中向其发送消息
    API_SetPositionUpdateCallback(handle, myPositionCallback, hMyLogWindow);
    API_SetAxisStatusCallback(handle, myAxisStatusCallback, nullptr);

    // 连接到设备
    API_OpenBoard(handle, "192.168.2.2", 6666);

    // ... 执行运动指令 ...
    // API_Ln(...);

    // 程序主循环（例如Windows消息循环）会处理来自回调的消息
    // 不再需要任何定时器来获取位置！

    // ... 清理 ...
    API_CloseBoard(handle);
    API_DeleteBoard(handle);

    return 0;
}
```

## 6. 结论

通过从轮询模型迁移到事件驱动的回调模型，`ADMotion` 库的性能、实时性和API易用性都将得到质的飞跃。这不仅能优化现有应用，更将为未来开发更复杂、更高效的控制程序奠定坚实的基础。建议优先投入资源完成此项架构升级。 