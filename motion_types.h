#pragma once
#ifndef MOTION_TYPES_H
#define MOTION_TYPES_H

#include "adconfig.h"
#include "motion_enums.h"
#include "motion_struct.h"

/**
 * @brief 版本信息结构体
 */
struct TVersion {
    short year;     // 版本年份
    short month;    // 版本月份
    short day;      // 版本日期
    short version;  // 版本号
    short chip;     // 芯片代码
    short reserve1;
    short reserve2;
};

/**
 * @brief 卡信息结构体
 */
struct TCardInfo {
    short AxisCount;
    short CRDCount;
    short ImportCount;
    short OutportCount;
    short CardType;
    short CardClass;
    char  CardName[32];
    char  CardSN[32];
    char  CardInfo[64];
};


/**
 * @brief 坐标系缓冲IO结构体
 */
struct TCrdBufIO {
    short          type;
    unsigned short doType;
    unsigned short doMask;
    unsigned short doValue;
    short          reserve0;
};

/**
 * @brief 坐标系缓冲DA结构体
 */
struct TCrdBufDA {
    short type;
    short channel;
    short daValue;
    short reserve0;
    short reserve1;
};

/**
 * @brief 坐标系缓冲延时结构体
 */
struct TCrdBufDelay {
    short          type;
    unsigned short delayTime;
    short          reserve0;
    short          reserve1;
    short          reserve2;
};

/**
 * @brief 坐标系缓冲限位结构体
 */
struct TCrdBufLmts {
    short type;
    short isLmtsOn;
    short axis;
    short limitType;
    short reserve0;
};

/**
 * @brief 坐标系缓冲停止IO结构体
 */
struct TCrdBufStopIO {
    short type;
    short axis;
    short stopType;
    short inputType;
    short inputIndex;
};

/**
 * @brief 坐标系缓冲运动结构体
 */
struct TCrdBufMotion {
    short type;
    short subType;
    short axis;
    short model;
    short reserve0;
};

/**
 * @brief 坐标系缓冲触发结构体
 */
struct TCrdBufTrigger {
    short          type;
    unsigned short triCount;
    unsigned short preOffset;
    short          reserve0;
    short          reserve1;
};

/**
 * @brief 缓冲数据联合体
 */
union BufData {
    TCrdBufIO      ioData;
    TCrdBufDA      daData;
    TCrdBufDelay   delayData;
    TCrdBufLmts    lmstData;
    TCrdBufStopIO  stopIoData;
    TCrdBufMotion  motionData;
    TCrdBufTrigger triggerData;
};

/**
 * @brief 坐标系数据结构体
 */
struct TCrdData {
    short   active[5];
    short   motionType;
    short   circlePlat;
    double  pos[5];
    double  radius;
    short   circleDir;
    double  center[3];
    double  midPoint[3];
    double  vel;
    double  acc;
    double  startVector[3];
    double  endVector[3];
    double  velStart;
    double  velEnd;
    double  velEndAdjust;
    double  length;
    double  crdTrans[9];
    int32_t height;
    double  pitch;
    double  transL;
    double  startPos[5];
    BufData bufData;
};



// 为了保持向后兼容性
typedef struct SpeedIOPoint TSpeedIOPoint;

#endif // MOTION_TYPES_H
