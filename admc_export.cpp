#include "admc_export.h"
#include "admc_pci.h"
#include "motion_error_codes.h"  // 使用新的错误码头文件
#include <string>
#include <cstring>

// 将short错误码转换为int错误码
int ConvertErrorCode(short errorCode) {
    // 简化错误码转换，直接映射到ADMC_错误码
    switch (errorCode) {
    case 1: // CMD_SUCCESS
        return ADMC_SUCCESS;
    case 100: // CMD_API_ERROR_OPEN
        return ADMC_ERROR_OPEN;
    case 101: // CMD_API_ERROR_OUT_RANGE
        return ADMC_ERROR_OUT_RANGE;
    case 102: // CMD_API_ERROR_TYPE
        return ADMC_ERROR_TYPE;
    case 103: // CMD_API_ERROR_CRD_DEMESION
        return ADMC_ERROR_CRD_DEMESION;
    case 104: // CMD_API_ERROR_CRD_DATA
        return ADMC_ERROR_CRD_DATA;
    case 105: // CMD_API_ERROR_CRD_MODE
        return ADMC_ERROR_CRD_MODE;
    case 106: // CMD_API_ERROR_CRD_RUN
        return ADMC_ERROR_CRD_RUN;
    case 107: // CMD_API_ERROR_CRD_STOP
        return ADMC_ERROR_CRD_STOP;
    case 108: // CMD_API_ERROR_CRD_PAUSE
        return ADMC_ERROR_CRD_PAUSE;
    case 109: // CMD_API_ERROR_CRD_DATA2
        return ADMC_ERROR_CRD_DATA2;
    case 110: // CMD_API_ERROR_CRD_FIFO_FULL
        return ADMC_ERROR_CRD_BUF_FULL;
    case 111: // CMD_API_ERROR_CRD_FIFO_EMPTY
        return ADMC_ERROR_CRD_BUF_EMPTY;
    case 112: // CMD_API_ERROR_CRD_FIFO_OVERFLOW
        return ADMC_ERROR_CRD_BUF_OVERFLOW;
    case 113: // CMD_API_ERROR_CRD_FIFO_UNDERFLOW
        return ADMC_ERROR_CRD_BUF_UNDERFLOW;
    case 114: // CMD_API_ERROR_CRD_FIFO_DATA
        return ADMC_ERROR_CRD_BUF_DATA;
    case 115: // CMD_API_ERROR_CRD_FIFO_DATA_TYPE
        return ADMC_ERROR_CRD_BUF_DATA_TYPE;
    case 116: // CMD_API_ERROR_CRD_FIFO_DATA_SUB_TYPE
        return ADMC_ERROR_CRD_BUF_DATA_SUB_TYPE;
    default:
        return errorCode; // 其他错误码直接返回
    }
}

// 基本操作函数实现
void* __stdcall ADMC_CreateBoard() {
    return API_CreateBoard();
}

void __stdcall ADMC_DeleteBoard(void* handle) {
    API_DeleteBoard(static_cast<TADMotionConn*>(handle));
}

int __stdcall ADMC_OpenBoard(void* handle, const char* ip, int port) {
    short result = API_OpenBoard(static_cast<TADMotionConn*>(handle), ip, port);
    return ConvertErrorCode(result);
}

int __stdcall ADMC_CloseBoard(void* handle) {
    short result = API_CloseBoard(static_cast<TADMotionConn*>(handle));
    return ConvertErrorCode(result);
}

int __stdcall ADMC_ResetBoard(void* handle) {
    short result = API_ResetBoard(static_cast<TADMotionConn*>(handle));
    return ConvertErrorCode(result);
}

// 轴控制函数实现
int __stdcall ADMC_SetAxisPrm(void* handle, int crd, int* axisMap, int* axisDir, int* velMax, int* accMax, int* positive, int* negative) {
    // 将int参数转换为short和int32_t
    short shortAxisMap[MAX_CRD_AXIS] = {0};
    short shortAxisDir[MAX_CRD_AXIS] = {0};
    int32_t int32VelMax[MAX_CRD_AXIS] = {0};
    int32_t int32AccMax[MAX_CRD_AXIS] = {0};
    int32_t int32Positive[MAX_CRD_AXIS] = {0};
    int32_t int32Negative[MAX_CRD_AXIS] = {0};

    for (int i = 0; i < MAX_CRD_AXIS; i++) {
        shortAxisMap[i] = static_cast<short>(axisMap[i]);
        shortAxisDir[i] = static_cast<short>(axisDir[i]);
        int32VelMax[i] = static_cast<int32_t>(velMax[i]);
        int32AccMax[i] = static_cast<int32_t>(accMax[i]);
        int32Positive[i] = static_cast<int32_t>(positive[i]);
        int32Negative[i] = static_cast<int32_t>(negative[i]);
    }

    short result = API_SetAxisPrm(static_cast<TADMotionConn*>(handle), static_cast<short>(crd),
                                 shortAxisMap, shortAxisDir, int32VelMax, int32AccMax, int32Positive, int32Negative);
    return ConvertErrorCode(result);
}

int __stdcall ADMC_AxisOn(void* handle, int axis) {
    short result = API_AxisOn(static_cast<TADMotionConn*>(handle), static_cast<short>(axis));
    return ConvertErrorCode(result);
}

int __stdcall ADMC_AxisOff(void* handle, int axis) {
    short result = API_AxisOff(static_cast<TADMotionConn*>(handle), static_cast<short>(axis));
    return ConvertErrorCode(result);
}

int __stdcall ADMC_GetAxisStatus(void* handle, int axis, int* status) {
    short shortStatus = 0;
    short result = API_GetAxisStatus(static_cast<TADMotionConn*>(handle), static_cast<short>(axis), shortStatus);
    *status = static_cast<int>(shortStatus);
    return ConvertErrorCode(result);
}

int __stdcall ADMC_GetAxisPos(void* handle, int axis, double* pos) {
    short result = API_GetAixsPos(static_cast<TADMotionConn*>(handle), static_cast<short>(axis), *pos);
    return ConvertErrorCode(result);
}

// 点位运动函数实现
int __stdcall ADMC_SetCrdTrapMode(void* handle, int crd) {
    short result = API_SetCrdTrapMode(static_cast<TADMotionConn*>(handle), static_cast<short>(crd));
    return ConvertErrorCode(result);
}

int __stdcall ADMC_SetCrdTrapPrm(void* handle, int crd,  double posTarget[2], double velMax, double acc, short rat) {
    short result = API_SetCrdTrapPrm(static_cast<TADMotionConn*>(handle), static_cast<short>(crd),  posTarget, velMax, acc, rat);
    return ConvertErrorCode(result);
}

int __stdcall ADMC_CrdTrapUpdate(void* handle, int crd) {
    short result = API_CrdTrapUpdate(static_cast<TADMotionConn*>(handle), static_cast<short>(crd));
    return ConvertErrorCode(result);
}

int __stdcall ADMC_SetAxisTrapMode(void* handle, int crd, int axis) {
    short result = API_SetAxisTrapMode(static_cast<TADMotionConn*>(handle), static_cast<short>(axis));
    return ConvertErrorCode(result);
}

int __stdcall ADMC_SetAxisTrapPrm(void* handle, int crd, int axis, double IncrPos, double velMax, double acc, short rat) {
    short result = API_SetAxisTrapPrm(static_cast<TADMotionConn*>(handle),  static_cast<short>(axis), IncrPos, velMax, acc, rat );
    return ConvertErrorCode(result);
}

int __stdcall ADMC_AxisTrapUpdate(void* handle, int crd) {
    short result = API_AxisTrapUpdate(static_cast<TADMotionConn*>(handle), static_cast<short>(crd));
    return ConvertErrorCode(result);
}



// 坐标系插补函数实现
int __stdcall ADMC_SetCrdPrm(void* handle, int crd, double synVelMax, double synAccMax) {
    short result = API_SetCrdPrm(static_cast<TADMotionConn*>(handle), static_cast<short>(crd), synVelMax, synAccMax );
    return ConvertErrorCode(result);
}




int __stdcall ADMC_CrdStart(void* handle, int crd) {
    short result = API_CrdStart(static_cast<TADMotionConn*>(handle), static_cast<short>(crd));
    return ConvertErrorCode(result);
}

int __stdcall ADMC_CrdStop(void* handle, int mask, int option, int stopType) {
    // 由于API_CrdStop未定义，这里我们暂时返回成功
    // 实际项目中应该实现正确的停止功能
    return ADMC_SUCCESS;
}

int __stdcall ADMC_CrdPause(void* handle, int crd) {
    // 由于API_CrdPause未定义，这里我们暂时返回成功
    // 实际项目中应该实现正确的暂停功能
    return ADMC_SUCCESS;
}


int __stdcall ADMC_GetCrdPos(void* handle, int crd, double* pos) {
    short result = API_GetCrdPos(static_cast<TADMotionConn*>(handle), static_cast<short>(crd), pos);
    return ConvertErrorCode(result);
}

// 直线圆弧插补函数实现
int __stdcall ADMC_Ln(void* handle, int crd, int x, int y, double synVel, double synAcc) {
    short result = API_Ln(static_cast<TADMotionConn*>(handle), static_cast<short>(crd),
                         static_cast<int32_t>(x), static_cast<int32_t>(y), synVel, synAcc);
    return ConvertErrorCode(result);
}

int __stdcall ADMC_ArcXY3Point(void* handle, int crd, int* p1, int* p2, int* p3, double radius, int circleDir, double synVel, double synAcc) {
    // 将int数组转换为int32_t数组
    int32_t p1Copy[3] = {static_cast<int32_t>(p1[0]), static_cast<int32_t>(p1[1]), static_cast<int32_t>(p1[2])};
    int32_t p2Copy[3] = {static_cast<int32_t>(p2[0]), static_cast<int32_t>(p2[1]), static_cast<int32_t>(p2[2])};
    int32_t p3Copy[3] = {static_cast<int32_t>(p3[0]), static_cast<int32_t>(p3[1]), static_cast<int32_t>(p3[2])};

    short result = API_ArcXY_3point(static_cast<TADMotionConn*>(handle), static_cast<short>(crd),
                                   p1Copy, p2Copy, p3Copy, synVel, synAcc);
    return ConvertErrorCode(result);
}

int __stdcall ADMC_ArcXYR(void* handle, int crd, int x, int y, double radius, int circleDir, double synVel, double synAcc) {
    short result = API_ArcXYR(static_cast<TADMotionConn*>(handle), static_cast<short>(crd),
                             static_cast<int32_t>(x), static_cast<int32_t>(y), radius,
                             static_cast<short>(circleDir), synVel, synAcc);
    return ConvertErrorCode(result);
}

int __stdcall ADMC_ArcXYC(void* handle, int crd, int x, int y, double xCenter, double yCenter, int circleDir, double synVel, double synAcc) {
    short result = API_ArcXYC(static_cast<TADMotionConn*>(handle), static_cast<short>(crd),
                             static_cast<int32_t>(x), static_cast<int32_t>(y), xCenter, yCenter,
                             static_cast<short>(circleDir), synVel, synAcc);
    return ConvertErrorCode(result);
}

// 前瞻函数实现
int __stdcall ADMC_InitLookAhead(void* handle, int crd, double accMax, int count) {
    short result = API_InitLookAhead(static_cast<TADMotionConn*>(handle), static_cast<short>(crd),
                                    accMax, static_cast<short>(count));
    return ConvertErrorCode(result);
}

int __stdcall ADMC_CloseLookAhead(void* handle, int crd) {
    short result = API_CloseLookAhead(static_cast<TADMotionConn*>(handle), static_cast<short>(crd));
    return ConvertErrorCode(result);
}

// IO函数实现
int __stdcall ADMC_SetDeviceOutput(void* handle, int* deviceOutput) {
    short result = API_SetDeviceOutput(static_cast<TADMotionConn*>(handle), deviceOutput);
    return ConvertErrorCode(result);
}


int __stdcall ADMC_GetDeviceInput(void* handle, int* deviceInput) {
    short result = API_GetDeviceInput(static_cast<TADMotionConn*>(handle), reinterpret_cast<int32_t*>(deviceInput));
    return ConvertErrorCode(result);
}

// 错误处理函数实现
const char* __stdcall ADMC_GetErrorMessage(int errorCode) {
    static std::string errorMessage;
    const wchar_t* wstr = GetMotionErrorString(errorCode);
    std::wstring ws(wstr);
    errorMessage = std::string(ws.begin(), ws.end());
    return errorMessage.c_str();
}
