#include "motion_api.h"
#include "motion_error_codes.h"  // 使用新的错误码头文件

// 注册错误处理回调函数
int MotionAPI::registerErrorCallback(ErrorCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    int id = m_nextCallbackId++;
    m_errorCallbacks[id] = callback;
    return id;
}

// 取消注册错误处理回调函数
bool MotionAPI::unregisterErrorCallback(int callbackId) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    auto it = m_errorCallbacks.find(callbackId);
    if (it != m_errorCallbacks.end()) {
        m_errorCallbacks.erase(it);
        return true;
    }
    return false;
}

// 更新系统状态
void MotionAPI::updateSystemStatus(SystemStatus newStatus) {
    if (m_systemStatus != newStatus) {
        m_systemStatus = newStatus;
        
        // 调用所有状态回调函数
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        for (const auto& callback : m_statusCallbacks) {
            callback.second(newStatus);
        }
        
        // 记录状态变化
        m_conn.log(LOG_INFO, "系统状态变化: %s", getSystemStatusDescription(newStatus).c_str());
    }
}

// 记录错误信息
void MotionAPI::logError(MotionErrorCode code, const std::string& operation) {
    std::string errorDetails = operation + " 失败: " + getMotionErrorDetails(code);
    m_lastErrorDetails = errorDetails;
    
    // 记录错误日志
    m_conn.log(LOG_ERROR, "%s", errorDetails.c_str());
    
    // 调用所有错误回调函数
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    for (const auto& callback : m_errorCallbacks) {
        callback.second(code, errorDetails);
    }
    
    // 如果是严重错误，更新系统状态
    if (code != MotionErrorCode::Success && 
        getErrorCategory(code) != ErrorCategory::None && 
        getErrorCategory(code) != ErrorCategory::Parameter) {
        updateSystemStatus(SystemStatus::Error);
    }
}
