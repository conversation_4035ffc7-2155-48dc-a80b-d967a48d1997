﻿
#pragma once
#include "adconfig.h"
#include "admc_info.h"
#include "motion_types.h"
#include "motion_enums.h"

/**********************************************************************/
/*插补接口指令 - 为了向后兼容性保留的宏定义，新代码应使用CrdCmdType枚举类*/
/**********************************************************************/
#define CRD_CMD_TYPE_LINE     0 //直线运动 0
#define CRD_CMD_TYPE_ARC      1 //圆弧 1
#define CRD_CMD_TYPE_HELIX    2 //平面螺旋线
#define CRD_CMD_TYPE_3DARC    3 // 3D圆弧 3
#define CRD_CMD_TYPE_3DHELIX  4 // 3维空间螺旋线 4
#define CRD_CMD_TYPE_BEZIER   5 // Bezier插补
#define CRD_CMD_TYPE_G0LINE   6 // G00运动
#define CRD_CMD_TYPE_FIVEAXIS 7 //五轴插补
#define CRD_CMD_TYPE_JUMP     8 //点跳

#define CRD_CMD_TYPE_BUFDATA 10 // buf指令数据

#define CRD_CMD_TYPE_RETURN_PAUSE_POS 11

// 缓冲数据类型 - 为了向后兼容性保留的宏定义，新代码应使用CrdBufDataType枚举类
#define CRD_BUFDATA_BUFIO      1
#define CRD_BUFDATA_BUFDA      2
#define CRD_BUFDATA_BUFDELAY   3
#define CRD_BUFDATA_BUFLMTS    4
#define CRD_BUFDATA_BUFSTOPIO  5
#define CRD_BUFDATA_BUFMOTION  6
#define CRD_BUFDATA_BUFTRIGGER 7

// 缓冲数据子类型 - 为了向后兼容性保留的宏定义，新代码应使用CrdBufDataSubType枚举类
#define CRD_BUFDATA_SUB_TYPE_MOVE 0
#define CRD_BUFDATA_SUB_TYPE_GEAR 1

// 圆弧所在平面定义 - 为了向后兼容性保留的宏定义，新代码应使用CrdArcPlane枚举类
#define CRD_ARC_XY 0
#define CRD_ARC_YZ 1
#define CRD_ARC_ZX 2

// 坐标系变换类型 - 为了向后兼容性保留的宏定义，新代码应使用CrdTransType枚举类
#define CRD_TRANS_TYPE_L 1

// 常量定义
#define PI         3.141592653589793
#define ZERO       0.000001 //脉冲
#define SERVO_TIME 0.25

#include <string>

typedef struct
{
    short isFirstMinLine;
    double pos[MAX_CRD_AXIS];             //存储上一段的目标位置  作为下一段起点
} TCrdApiPrm;//程序插补参数

typedef struct
{
    double x;
    double y;
    double z;
} Point; //定义一个坐标结构体
class TADMotionConn;

#include "lookahead_buffer.h"

/**
 * @brief 前瞻参数结构体
 * 使用智能指针管理缓冲区，确保资源的正确释放
 */
typedef struct TLookAheadPrm
{
    short     crd;  // 1,2
    short     enable;
    short     count;     //前瞻段数
    double    accMax;    //各轴的最大加速度
    short     nBufCount; // buf个数
    short     nBufHead;  //代码存储头指针
    short     nBufRear;  //代码存储尾指针

    // 使用智能指针管理缓冲区
    std::shared_ptr<LookAheadBuffer> buffer;

    // 保留原有指针，用于向后兼容
    // 这些指针由buffer管理，不需要手动释放
    TCrdData* pHostData;     // 指向buffer->data()
    TCrdData* pCmdBufInPtr;  // 指令buff数据存放指针
    TCrdData* pCmdBufOutPtr; // 指令buff数据取出指针

    double    lookAheadInitVel;
    short     endflag; //结束所有前瞻数据，全部输出标志

    // 初始化函数，确保所有成员都被正确初始化
    void initialize() {
        crd = 0;
        enable = 0;
        count = 0;
        accMax = 0.0;
        nBufCount = 0;
        nBufHead = 0;
        nBufRear = 0;

        // 创建缓冲区
        if (!buffer) {
            buffer = std::make_shared<LookAheadBuffer>(200);
        }

        // 初始化指针
        pHostData = buffer->data();
        pCmdBufInPtr = pHostData;
        pCmdBufOutPtr = pHostData;

        lookAheadInitVel = 0.0;
        endflag = 0;
    }

    // 重置函数，重置所有状态
    void reset() {
        nBufCount = 0;
        nBufHead = 0;
        nBufRear = 0;

        // 清空缓冲区
        if (buffer) {
            buffer->clear();
        }

        // 重置指针
        pHostData = buffer ? buffer->data() : nullptr;
        pCmdBufInPtr = pHostData;
        pCmdBufOutPtr = pHostData;

        lookAheadInitVel = 0.0;
        endflag = 0;
    }
} TLookAheadPrm;

typedef struct
{
    TCardInfo     CardInfo;
    TCrdApiPrm    gCrdApiPrm[MAX_CRD]; //一张卡支持的最多坐标系
    TLookAheadPrm gLookAheadPrm[MAX_CRD];

} TCardDef;



void InitCrd(TADMotionConn* handle, short crd);

short CrdPrmCheck(
    TADMotionConn* handle, short crd, double pos1, double pos2, double pos3, double pos4, double pos5, double pos6);

short CrdRunCheck(TADMotionConn* handle, short crd);

short CrdBezierPrmCheck(TADMotionConn* handle, double L, double maxVel);

short CrdBezierRunCheck(TADMotionConn* handle, short crd, short isBezierCmd);

short CrdDataProcess(TADMotionConn* handle, short crd, TCrdData* pCrdData);

short CrdPreProcessing(TADMotionConn* handle, short crd, TCrdData* pCrdData);
//当前数据类型是不是插补数据
short IsIntpType(short type);

short IsIntpLineOrArcType(short type);

short SendCrdData(TADMotionConn* handle, short crd, TCrdData* pCrdData);

short TransitionProcessing(TADMotionConn* handle, short crd, TCrdData* pCrdData);

short MC_insert_new_line_UnLookAhead(TADMotionConn* handle, short crd, TCrdData* pCrdData);
short MC_insert_line_Processing(TADMotionConn* handle, short crd, TCrdData* pCrdData);

short IsBuffFull(TADMotionConn* handle, short crd);
//判断buffer是否空
short IsBuffEmpty(TADMotionConn* handle, short crd);

short InsertNewLine(TADMotionConn* handle, short crd, TCrdData* pCrdData);

short InitLookAheadBuff(TADMotionConn* handle, short crd);
// short API_CloseLookAhead(TADMotionConn* handle, short crd);
short MC_CloseLookAheadTab(TADMotionConn* handle, short crd);
short GetBuffCount(TADMotionConn* handle, short crd);

short LookAheadHandle(TADMotionConn* handle, short crd, TLookAheadPrm* pLookAheadPrm);

void LookAheadPreProcess(TADMotionConn* handle, TLookAheadPrm* pData, short len, double accMax);

TCrdData* PointerMove(TCrdData* pSrc0, int32_t n, TLookAheadPrm* pLookAheadPrm);
