# ADAMotion 项目优化策略

## 1. 引言

本项目旨在对 ADAMotion 运动控制卡的 C++ API 进行全面的优化，遵循 C++11/14 标准，重点提升代码的安全性、可维护性、可读性和现代性。此文档将作为优化过程的指导和记录。
修改代码的时候要尽量加上中文注释.

## 2. 核心优化方向

参考 `motion.mdc` 中的指导原则，并结合当前代码库的状况，主要优化方向如下：

### 2.1. API 设计与 C++ 封装层 (`ADMotionPackage.h`)

*   **目标**: 提供一个更安全、更易用、更符合 C++ 习惯的接口。
*   **具体措施**:
    *   **`TADMotionConn` 生命周期管理**:
        *   `API_CreateBoard()` 返回裸指针 `TADMotionConn*`，`API_DeleteBoard()` 手动删除。这在 C++ 封装层应改为使用智能指针（如 `std::unique_ptr` 或 `std::shared_ptr`）。
        *   考虑将 `TADMotion` 类的构造函数/析构函数管理 `TADMotionConn` 的创建和销毁，或者提供工厂方法返回智能指针。
    *   **参数类型与返回值**:
        *   审视C API函数的参数（如C风格数组、指针传递的输出参数），在C++封装层提供更安全的替代方案（如 `std::vector`, `std::string`, 返回 `std::optional` 或自定义结果类）。
        *   例如，`API_GetCrdPos(TADMotionConn* handle, short crd, double* pPos)`，在C++层可以封装为 `std::optional<std::array<double, 2>> GetCrdPos(short crd)`。
    *   **C++特性利用**:
        *   在 `TADMotion` 类的方法中，充分利用 `const` 正确性、`override` (如果有虚函数继承)、`noexcept` 等。
        *   考虑是否可以将某些回调机制用 `std::function` 和 lambda 表达式优化。

### 2.2. 内存管理 (`.cpp` 文件和核心逻辑)

*   **目标**: 杜绝内存泄漏和悬挂指针。
*   **具体措施**:
    *   **智能指针**: 全面替换手动 `new`/`delete`。
    *   **RAII**: 确保资源（如socket连接、文件句柄等，虽然本项目主要关注板卡连接）在对象生命周期内被正确管理。
    *   **容器使用**: 对于动态数组，使用 `std::vector`；固定大小数组，考虑 `std::array`。
    *   **特定模块优化 (例如 `lookahead.cpp`)**: 
        *   `lookahead.cpp` 中的 `TLookAheadPrm::pHostData` 指针管理的缓冲区，应考虑使用 `std::vector<TCrdData>` 或 `std::unique_ptr<TCrdData[]>` 进行替换，以实现自动内存管理和更安全的访问。

### 2.3. 代码现代化与重构 (`.cpp` 文件)

*   **目标**: 提升代码质量、可读性，利用现代C++特性。
*   **具体措施**:
    *   **C++11/14特性**:
        *   使用 `nullptr` 替代 `NULL`。
        *   使用 `auto` 进行类型推断。
        *   使用基于范围的 for 循环。
        *   使用 `enum class` 替代传统 `enum` 及相关的宏定义常量 (例如 `admc_info.h` 中的状态码、模式码)。
        *   适时使用 Lambda 表达式。
        *   全局移除自定义的 `FALSE`/`TRUE` 宏，统一使用 `false`/`true`。
    *   **消除重复代码**:
        *   如 `admc_pci.cpp` 中常见的 `axis / 2` 和 `axis % 2` 计算，可以封装成辅助函数或方法。
        *   相似的API实现逻辑（如JOG控制的多个方向函数）看是否可以合并或通过参数化简化。
    *   **缓冲区操作优化 (例如 `lookahead.cpp`)**:
        *   `lookahead.cpp` 中手动实现的循环缓冲区逻辑和 `memcpy` 操作，在改为使用 `std::vector` 后，可以利用其成员函数或标准算法进行更安全、简洁的操作。
    *   **函数与类设计**:
        *   确保函数和类的单一职责。
        *   过长的函数应进行拆分。
    *   **命名规范**: 检查并统一命名规范。
    *   **注释**: 清理过时或不必要的注释，补充关键逻辑的说明。

### 2.4. 头文件组织与解耦

*   **目标**: 实现清晰的头文件职责，减少不必要的耦合，将数据结构定义与实现细节分离。
*   **具体措施**:
    *   **创建纯净的类型定义文件**: 
        *   创建一个或多个新的头文件 (如 `motion_types.h`, `motion_enums.h`) 用于存放被公共API (`admc_pci.h`) 使用的核心数据结构 (如 `TJogPrm`, `TTrapPrm`, `TCrdData`, `TSpeedIOPoint` 等) 和相关的枚举类型 (`enum class`)。
    *   **重构 `admc_info.h`**:
        *   将核心结构体定义从 `admc_info.h` 迁移到新的类型定义文件。
        *   审查 `admc_info.h` 中剩余的宏定义常量：
            *   对属于API契约的常量，考虑用 `enum class` 替代并迁移到新的枚举定义文件。
            *   对仅用于内部实现的常量，应限制其作用域（移至 `.cpp` 或内部头文件）。
            *   移除已废弃或不必要的常量。
        *   `admc_info.h` 在重构后，其内容和被包含的范围应大幅缩减。
    *   **更新包含关系**:
        *   `admc_pci.h` 应 `#include` 新的类型和枚举定义头文件。
        *   确保所有 `.cpp` 文件包含正确的头文件，且只包含必需的声明。
    *   **移除或限制内部变量/宏的暴露**: 确保API使用者不会接触到库的内部实现细节。

### 2.5. 错误处理机制

*   **目标**: 提供清晰、一致、易于处理的错误信息。
*   **具体措施**:
    *   **C API层面**: 保持返回 `short` 错误码的兼容性。
    *   **C++ 封装层面 (`TADMotion` 类)**:
        *   可以将 `short` 错误码转换为更有意义的 `enum class` 错误类型。
        *   可以提供方法将错误码转换为描述性字符串。
        *   对于严重错误或不期望的函数调用顺序，可以考虑在C++层抛出异常（需评估库的使用场景是否适合异常）。
        *   提供 `GetLastError()` 或类似机制获取详细错误信息。

### 2.6. 模块化与可维护性

*   **目标**: 使代码结构更清晰，易于理解和扩展。
*   **具体措施**:
    *   审视各文件职责（如 `card.cpp`, `ioctrl.cpp` 等），确保高内聚低耦合。
    *   移除已废弃或不再使用的代码段 (如CAN总线相关代码已处理)。

### 2.7. 构建系统 (`ADMotion.pro`)

*   **目标**: 确保构建配置的正确性和可维护性。
*   **具体措施**:
    *   解决已知问题 (如 `QMAKE_POST_LINK` 的文件复制问题)。
    *   确保 `DEFINES += MC_API_EXPORT` 正确处理库的导入导出。
    *   审视 `INCLUDEPATH` 和 `LIBS` 的配置，确保路径和库依赖的准确性。

### 2.8. 日志系统

*   **目标**: 提供一个灵活、可配置、易于使用的日志系统，方便调试和问题追踪。
*   **具体措施**:
    *   **选择或实现日志库**: 
        *   可以考虑集成轻量级的第三方C++日志库（如 spdlog, glog等，需评估依赖和项目需求），或者实现一个简单的内部日志类。
    *   **日志级别**: 支持不同的日志级别 (如 DEBUG, INFO, WARNING, ERROR, FATAL)。
    *   **日志格式**: 可配置的日志格式，至少包含时间戳、级别、模块名（或文件名/函数名）、日志内容。
    *   **日志输出**: 支持输出到控制台、文件，甚至网络（如果需要）。
    *   **集成**: 在关键API调用、重要状态变更、错误发生等地方加入日志记录。
    *   **性能考虑**: 日志记录不应显著影响库的性能，特别是在性能敏感的路径上。

## 3. 优化步骤建议 (调整顺序和内容以反映新的重点)

1.  **阶段一：基础清理、构建修复与头文件初步整理**
    *   [x] 移除废弃的CAN总线相关接口及其实现。
    *   [ ] 彻底解决 `.pro` 文件中的 `QMAKE_POST_LINK` 文件复制问题。
    *   [ ] **头文件重构初步**: 
        *   创建 `motion_types.h` 和 `motion_enums.h` (或类似名称)。
        *   将 `admc_info.h` 中最核心的、`admc_pci.h` 直接依赖的结构体定义 (如 `TJogPrm`, `TTrapPrm`, `TCrdData`, `TSpeedIOPoint`) 迁移到 `motion_types.h`。
        *   将 `admc_info.h` 中一部分明确的、API相关的宏常量用 `enum class` 替换并迁移到 `motion_enums.h`。
        *   更新 `admc_pci.h` 以包含新的类型和枚举头文件。
        *   全局替换 `NULL` 为 `nullptr`。
        *   全局移除自定义的 `FALSE`/`TRUE` 宏，统一使用 `false`/`true`。
    *   [ ] 引入基础的日志框架/选型，并在几个关键API入口处添加初步日志。
    *   [ ] 简单应用 `auto` 和范围for循环在非核心、易于验证的模块。

2.  **阶段二：核心模块内存管理、封装优化与头文件深化整理**
    *   [ ] **`TADMotionConn` 生命周期**: 修改 `API_CreateBoard` / `API_DeleteBoard`，在 `TADMotion` 类中通过构造/析构或工厂方法管理 `TADMotionConn` 的生命周期 (引入智能指针)。
    *   [ ] **`lookahead.cpp` 缓冲区优化**: 将 `pHostData` 改造为使用 `std::vector<TCrdData>` 或 `std::unique_ptr<TCrdData[]>`，并相应修改缓冲区管理逻辑。
    *   [ ] **`ADMotionPackage.h` 封装**: 逐步封装 `TADMotion` 类中的方法，使其参数和返回值更符合C++风格 (例如，使用 `std::vector`, `std::optional` 等)，优先处理与 `lookahead` 相关及其他常用接口。
    *   [ ] **头文件深化整理**: 
        *   继续审查 `admc_info.h` 和其他可能包含公共类型定义的头文件 (如 `card.h`, `admc.h`)，将更多公共类型和枚举迁移出来。
        *   处理内部宏定义和变量，确保它们不通过公共头文件泄露。
    *   [ ] 在此阶段，对修改的核心模块增加更详细的日志记录。

3.  **阶段三：内部实现代码重构与日志完善 (`.cpp` 文件)**
    *   [ ] 深入应用C++11/14特性于其他 `.cpp` 文件 (包括 `enum class` 的广泛使用)。
    *   [ ] 识别并重构重复代码段。
    *   [ ] 优化数据结构使用（如C风格数组改为 `std::vector` 或 `std::array`）。
    *   [ ] 全面铺开日志记录到各个模块和重要操作。

4.  **阶段四：错误处理机制完善**
    *   [ ] 在C++封装层引入更完善的错误处理（如 `enum class` 错误码，错误信息转换）。
    *   [ ] 结合日志系统，确保错误能被有效记录。

5.  **阶段五：代码审查、测试与文档更新**
    *   [ ] 对所有改动进行代码审查。
    *   [ ] 补充或完善单元测试（如果项目有测试框架）。
    *   [ ] 更新相关文档，说明API变动和新增的日志系统使用方法。

## 4. 注意事项

*   每次重要改动后都应进行编译和（如果可能）测试，确保没有引入回归错误。
*   小步快跑，逐步迭代。
*   对于复杂的改动，可以先在分支上进行。

---

接下来，我们可以从**阶段一**新调整的重点“头文件重构初步”开始，或者您有其他优先想处理的部分，请告诉我。 